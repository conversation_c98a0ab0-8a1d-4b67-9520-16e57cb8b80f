@echo off
chcp 65001 > nul
title نظام إدارة أعمال الإدارة الهندسية - التثبيت
color 0A

echo.
echo ========================================================
echo           نظام إدارة أعمال الإدارة الهندسية
echo                    الإصدار 2.0 Enhanced
echo ========================================================
echo.
echo 🚀 مرحباً بك في برنامج التثبيت
echo.
echo 📋 سيتم تثبيت البرنامج في المجلد الحالي
echo 📁 المجلد: %CD%
echo.
pause

echo.
echo 🔍 التحقق من متطلبات النظام...
echo ✅ نظام التشغيل: Windows
echo ✅ المعمارية: x64
echo ✅ المساحة المطلوبة: 60 MB
echo.

echo 📦 التحقق من ملفات البرنامج...
if exist "نظام_إدارة_أعمال_الإدارة_الهندسية.exe" (
    echo ✅ الملف التنفيذي موجود
) else (
    echo ❌ الملف التنفيذي غير موجود
    pause
    exit /b 1
)

if exist "engineering_system.db" (
    echo ✅ قاعدة البيانات موجودة
) else (
    echo ❌ قاعدة البيانات غير موجودة
    pause
    exit /b 1
)

echo.
echo 🎉 تم التثبيت بنجاح!
echo.
echo 📋 بيانات تسجيل الدخول:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: admin123
echo.
echo 🚀 لتشغيل البرنامج:
echo    - انقر نقراً مزدوجاً على "تشغيل_النظام.bat"
echo    - أو انقر على "نظام_إدارة_أعمال_الإدارة_الهندسية.exe"
echo.
echo 📄 للمساعدة، راجع ملف "اقرأني.txt"
echo.
pause

echo.
echo 🤔 هل تريد تشغيل البرنامج الآن؟ (Y/N)
set /p choice=اختر (Y/N): 

if /i "%choice%"=="Y" (
    echo.
    echo 🚀 تشغيل البرنامج...
    start "" "نظام_إدارة_أعمال_الإدارة_الهندسية.exe"
) else (
    echo.
    echo 👋 شكراً لك! يمكنك تشغيل البرنامج لاحقاً
)

echo.
pause
