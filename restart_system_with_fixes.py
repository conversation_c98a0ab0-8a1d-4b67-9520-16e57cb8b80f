#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة تشغيل النظام مع الإصلاحات الجديدة للخطوط العربية
Restart system with new Arabic fonts fixes
"""

import sys
import os
import importlib

def reload_modules():
    """إعادة تحميل الوحدات المُحدثة"""
    modules_to_reload = [
        'reports_management',
        'maintenance_management', 
        'buildings_management'
    ]
    
    print("🔄 إعادة تحميل الوحدات المُحدثة...")
    
    for module_name in modules_to_reload:
        if module_name in sys.modules:
            print(f"   🔄 إعادة تحميل {module_name}...")
            importlib.reload(sys.modules[module_name])
            print(f"   ✅ تم إعادة تحميل {module_name}")
        else:
            print(f"   ⚠️ {module_name} غير محمل مسبقاً")
    
    print("✅ تم إعادة تحميل جميع الوحدات")

def clear_cache():
    """مسح ذاكرة التخزين المؤقت"""
    print("🧹 مسح ذاكرة التخزين المؤقت...")
    
    # مسح ملفات .pyc
    import glob
    pyc_files = glob.glob("__pycache__/*.pyc")
    for pyc_file in pyc_files:
        try:
            os.remove(pyc_file)
            print(f"   🗑️ تم حذف {pyc_file}")
        except:
            pass
    
    print("✅ تم مسح ذاكرة التخزين المؤقت")

def test_fixes():
    """اختبار الإصلاحات"""
    print("🧪 اختبار الإصلاحات...")
    
    try:
        # اختبار استيراد الوحدات
        from reports_management import ReportsManagementWindow
        from maintenance_management import MaintenanceManagementWindow
        from buildings_management import BuildingsManagementWindow
        
        print("✅ تم استيراد جميع الوحدات بنجاح")
        
        # اختبار إعداد الخطوط العربية
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء مدير قاعدة البيانات وهمي
        class DummyDBManager:
            pass
        
        class DummyAuthManager:
            pass
        
        db_manager = DummyDBManager()
        auth_manager = DummyAuthManager()
        
        # اختبار نافذة التقارير
        print("   🧪 اختبار نافذة التقارير...")
        reports_window = ReportsManagementWindow(root, db_manager, auth_manager)
        arabic_support = reports_window.setup_arabic_pdf_support()
        if arabic_support:
            print("   ✅ دعم الخطوط العربية يعمل في نافذة التقارير")
        else:
            print("   ⚠️ سيتم استخدام الخطوط الافتراضية في نافذة التقارير")
        
        # اختبار نافذة الصيانة
        print("   🧪 اختبار نافذة الصيانة...")
        maintenance_window = MaintenanceManagementWindow(root, db_manager, auth_manager)
        arabic_support = maintenance_window.setup_arabic_pdf_support()
        if arabic_support:
            print("   ✅ دعم الخطوط العربية يعمل في نافذة الصيانة")
        else:
            print("   ⚠️ سيتم استخدام الخطوط الافتراضية في نافذة الصيانة")
        
        # اختبار نافذة المباني
        print("   🧪 اختبار نافذة المباني...")
        buildings_window = BuildingsManagementWindow(root, db_manager, auth_manager)
        arabic_support = buildings_window.setup_arabic_pdf_support()
        if arabic_support:
            print("   ✅ دعم الخطوط العربية يعمل في نافذة المباني")
        else:
            print("   ⚠️ سيتم استخدام الخطوط الافتراضية في نافذة المباني")
        
        root.destroy()
        print("✅ جميع الاختبارات نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

def start_system():
    """بدء تشغيل النظام"""
    print("🚀 بدء تشغيل النظام مع الإصلاحات...")
    
    try:
        from main_gui import MainApplication
        
        print("✅ تم تحميل النظام الرئيسي")
        print("🎯 فتح النظام...")
        print("=" * 50)
        print("📋 بيانات تسجيل الدخول:")
        print("   👤 اسم المستخدم: admin")
        print("   🔒 كلمة المرور: admin123")
        print("=" * 50)
        
        app = MainApplication()
        app.start()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        import traceback
        traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    print("🔧 إعادة تشغيل النظام مع إصلاحات الخطوط العربية")
    print("=" * 60)
    
    # مسح ذاكرة التخزين المؤقت
    clear_cache()
    
    # إعادة تحميل الوحدات
    reload_modules()
    
    # اختبار الإصلاحات
    if test_fixes():
        print("\n🎉 الإصلاحات تعمل بشكل صحيح!")
        print("🚀 بدء تشغيل النظام...")
        print("\n" + "=" * 60)
        
        # بدء تشغيل النظام
        start_system()
    else:
        print("\n❌ هناك مشاكل في الإصلاحات")
        print("يرجى مراجعة الأخطاء أعلاه")

if __name__ == "__main__":
    main()
