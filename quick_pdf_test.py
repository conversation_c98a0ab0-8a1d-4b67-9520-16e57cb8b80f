#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لإنشاء PDF مع النص العربي المُصلح
Quick test for creating PDF with fixed Arabic text
"""

import os
from datetime import datetime

def create_test_pdf():
    """إنشاء ملف PDF تجريبي مع النص العربي"""
    try:
        from reportlab.lib.pagesizes import landscape, A4
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import platform
        
        print("🔍 إنشاء ملف PDF تجريبي...")
        
        # إعداد الخطوط العربية
        registered_fonts = {}
        
        if platform.system() == "Windows":
            font_paths = {
                'Arabic-Bold': "C:/Windows/Fonts/arialbd.ttf",
                'Arabic-Regular': "C:/Windows/Fonts/arial.ttf"
            }
        else:
            font_paths = {
                'Arabic-Bold': "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                'Arabic-Regular': "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
            }
        
        # تسجيل الخطوط
        for font_name, font_path in font_paths.items():
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont(font_name, font_path))
                    registered_fonts[font_name] = font_name
                    print(f"✅ تم تسجيل الخط: {font_name}")
                except Exception as e:
                    print(f"⚠️ فشل في تسجيل {font_name}: {e}")
        
        if not registered_fonts:
            print("⚠️ لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")
            registered_fonts = {'Arabic-Bold': 'Helvetica-Bold', 'Arabic-Regular': 'Helvetica'}
        
        # إنشاء ملف PDF
        current_dir = os.path.dirname(os.path.abspath(__file__))
        test_file_path = os.path.join(current_dir, "اختبار_سريع_للخطوط.pdf")
        
        doc = SimpleDocTemplate(
            test_file_path,
            pagesize=landscape(A4),
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm,
            title="اختبار سريع للخطوط العربية"
        )
        
        # إنشاء الأنماط
        styles = getSampleStyleSheet()
        
        title_font = 'Arabic-Bold' if 'Arabic-Bold' in registered_fonts else 'Arabic-Regular'
        normal_font = 'Arabic-Regular' if 'Arabic-Regular' in registered_fonts else 'Arabic-Bold'
        
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName=title_font,
            fontSize=24,
            spaceAfter=25,
            alignment=TA_CENTER,
            textColor=colors.Color(0.12, 0.23, 0.54)
        )
        
        normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            fontName=normal_font,
            fontSize=12,
            spaceAfter=10,
            alignment=TA_RIGHT,
            textColor=colors.black
        )
        
        # إنشاء المحتوى
        story = []
        
        # العنوان
        story.append(Paragraph("🔧 اختبار سريع للخطوط العربية المُصلحة 🔧", title_style))
        story.append(Spacer(1, 20))
        
        # النص العربي
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        arabic_text = f"""
📅 تاريخ الاختبار: {current_time}

🎯 الهدف من هذا الاختبار:
التحقق من أن إصلاحات الخطوط العربية تعمل بشكل صحيح في النظام.

✅ الإصلاحات المُطبقة:
• تم إصلاح تسجيل الخطوط في maintenance_management.py
• تم إصلاح تسجيل الخطوط في buildings_management.py  
• تم إصلاح استخدام الخطوط في reports_management.py

🔍 الخطوط المستخدمة:
• خط العنوان: {title_font}
• خط النص العادي: {normal_font}

📊 نتيجة الاختبار:
إذا كنت تقرأ هذا النص بوضوح ووضوح تام بدون أي رموز غريبة أو مربعات سوداء، 
فهذا يعني أن إصلاح مشكلة الخطوط العربية قد نجح بالكامل!

🎉 تهانينا! النظام الآن يدعم العربية بشكل كامل.

📋 التوصيات:
1. اختبر إنشاء تقارير من النظام الرئيسي
2. تحقق من جميع الوحدات (المشاريع، المباني، الصيانة)
3. تأكد من أن جميع التقارير تظهر النص العربي بوضوح

🔧 في حالة وجود مشاكل:
• تأكد من إعادة تشغيل النظام بالكامل
• تحقق من وجود الخطوط في النظام
• راجع رسائل الخطأ في وحدة التحكم
        """
        
        # إضافة النص إلى التقرير
        for line in arabic_text.strip().split('\n'):
            if line.strip():
                story.append(Paragraph(line.strip(), normal_style))
                story.append(Spacer(1, 5))
        
        # بناء المستند
        doc.build(story)
        
        # التحقق من إنشاء الملف
        if os.path.exists(test_file_path):
            file_size = os.path.getsize(test_file_path)
            print(f"✅ تم إنشاء ملف PDF تجريبي: {test_file_path}")
            print(f"📁 حجم الملف: {file_size:,} بايت")
            
            if file_size > 5000:
                print("✅ الملف يحتوي على محتوى صحيح")
                
                # محاولة فتح الملف
                try:
                    import subprocess
                    import platform
                    
                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['start', '', test_file_path], shell=True, check=False)
                        print("📖 تم فتح الملف للمراجعة")
                    elif system == "Darwin":  # macOS
                        subprocess.run(['open', test_file_path], check=False)
                        print("📖 تم فتح الملف للمراجعة")
                    elif system == "Linux":
                        subprocess.run(['xdg-open', test_file_path], check=False)
                        print("📖 تم فتح الملف للمراجعة")
                        
                except Exception as e:
                    print(f"⚠️ لا يمكن فتح الملف تلقائياً: {e}")
                
                return True
            else:
                print("⚠️ حجم الملف صغير، قد تكون هناك مشكلة")
                return False
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف PDF: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 اختبار سريع للخطوط العربية المُصلحة")
    print("=" * 50)
    
    success = create_test_pdf()
    
    if success:
        print("\n🎉 نجح الاختبار!")
        print("📖 يرجى فتح الملف والتحقق من ظهور النص العربي بوضوح")
        print("📁 الملف: اختبار_سريع_للخطوط.pdf")
        print("\n💡 إذا كان النص يظهر بوضوح، فقد تم إصلاح المشكلة!")
        print("🔄 يرجى إعادة تشغيل النظام الرئيسي لتطبيق الإصلاحات")
    else:
        print("\n❌ فشل الاختبار")
        print("🔧 يرجى مراجعة الأخطاء أعلاه")
