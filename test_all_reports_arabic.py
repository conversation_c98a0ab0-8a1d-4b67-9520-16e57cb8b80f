#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع التقارير مع الخطوط العربية المُصلحة
Comprehensive test for all reports with fixed Arabic fonts
"""

import sys
import os
import tkinter as tk
from datetime import datetime

# إضافة المسار الحالي لـ sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def create_test_report(module_name, content, file_name):
    """إنشاء تقرير تجريبي لوحدة معينة"""
    try:
        from reportlab.lib.pagesizes import landscape, A4
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import platform
        
        # إعداد الخطوط العربية
        registered_fonts = {}
        
        if platform.system() == "Windows":
            font_paths = {
                'Arabic-Bold': [
                    "C:/Windows/Fonts/arialbd.ttf",
                    "C:/Windows/Fonts/tahomabd.ttf"
                ],
                'Arabic-Regular': [
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/tahoma.ttf"
                ]
            }
        else:
            font_paths = {
                'Arabic-Bold': ["/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"],
                'Arabic-Regular': ["/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"]
            }
        
        # تسجيل الخطوط
        for font_name, paths in font_paths.items():
            for font_path in paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont(font_name, font_path))
                        registered_fonts[font_name] = font_name
                        break
                    except:
                        continue
        
        if not registered_fonts:
            registered_fonts = {'Arabic-Bold': 'Helvetica-Bold', 'Arabic-Regular': 'Helvetica'}
        
        # إنشاء ملف PDF
        test_file_path = os.path.join(current_dir, file_name)
        
        doc = SimpleDocTemplate(
            test_file_path,
            pagesize=landscape(A4),
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm,
            title=f"تقرير {module_name}",
            author="نظام إدارة أعمال الإدارة الهندسية"
        )
        
        # إنشاء الأنماط
        styles = getSampleStyleSheet()
        
        title_font = 'Arabic-Bold' if 'Arabic-Bold' in registered_fonts else 'Arabic-Regular'
        normal_font = 'Arabic-Regular' if 'Arabic-Regular' in registered_fonts else 'Arabic-Bold'
        
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName=title_font,
            fontSize=24,
            spaceAfter=25,
            alignment=TA_CENTER,
            textColor=colors.Color(0.12, 0.23, 0.54)
        )
        
        normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            fontName=normal_font,
            fontSize=12,
            spaceAfter=10,
            alignment=TA_RIGHT,
            textColor=colors.black
        )
        
        # إنشاء المحتوى
        story = []
        story.append(Paragraph(f"🏗️ تقرير {module_name} 🏗️", title_style))
        story.append(Spacer(1, 20))
        
        # إضافة المحتوى
        for line in content.strip().split('\n'):
            if line.strip():
                story.append(Paragraph(line.strip(), normal_style))
                story.append(Spacer(1, 5))
        
        # بناء المستند
        doc.build(story)
        
        if os.path.exists(test_file_path):
            file_size = os.path.getsize(test_file_path)
            print(f"✅ تم إنشاء تقرير {module_name}: {file_name} ({file_size:,} بايت)")
            return True
        else:
            print(f"❌ فشل في إنشاء تقرير {module_name}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير {module_name}: {e}")
        return False

def test_all_reports():
    """اختبار جميع التقارير"""
    print("🚀 اختبار شامل لجميع التقارير مع الخطوط العربية")
    print("=" * 60)
    
    current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
    
    # تقرير إدارة المشاريع
    projects_content = f"""
📊 تقرير إدارة المشاريع الهندسية

📅 تاريخ التقرير: {current_time}

🏗️ إحصائيات المشاريع:
• إجمالي المشاريع: 15 مشروع
• المشاريع المكتملة: 8 مشاريع
• المشاريع قيد التنفيذ: 5 مشاريع
• المشاريع المعلقة: 2 مشروع

📈 أنواع المشاريع:
- مشاريع البناء والتشييد: 6 مشاريع
- مشاريع الصيانة والتطوير: 4 مشاريع
- مشاريع التحديث والتجديد: 3 مشاريع
- مشاريع البنية التحتية: 2 مشروع

💰 الميزانيات:
• إجمالي الميزانية المخصصة: 2,500,000 ريال
• المبلغ المُنفق: 1,800,000 ريال
• المبلغ المتبقي: 700,000 ريال

⏰ الجدولة الزمنية:
✅ في الموعد المحدد: 10 مشاريع
⚠️ متأخرة قليلاً: 3 مشاريع
🔴 متأخرة كثيراً: 2 مشروع

🎯 التوصيات:
- تسريع العمل في المشاريع المتأخرة
- مراجعة الميزانيات المتبقية
- تحديث الجداول الزمنية
- تحسين التنسيق بين الفرق

📋 ملاحظات إضافية:
تم تحديث هذا التقرير ليعكس أحدث البيانات المتوفرة في النظام.
جميع الأرقام والإحصائيات محدثة حتى تاريخ إنشاء التقرير.
    """
    
    # تقرير إدارة المباني
    buildings_content = f"""
🏢 تقرير إدارة المباني والمرافق

📅 تاريخ التقرير: {current_time}

🏗️ إحصائيات المباني:
• إجمالي المباني: 12 مبنى
• المباني السكنية: 5 مباني
• المباني الإدارية: 4 مباني
• المباني الخدمية: 3 مباني

📐 المساحات:
• إجمالي المساحة المبنية: 15,000 متر مربع
• المساحة المستغلة: 12,500 متر مربع
• المساحة الفارغة: 2,500 متر مربع

🔧 حالة المباني:
✅ ممتازة: 7 مباني
⚠️ جيدة: 3 مباني
🔴 تحتاج صيانة: 2 مبنى

🏠 تفاصيل المباني:
- مبنى الإدارة الرئيسي: 3 طوابق، 1,200 م²
- مبنى الخدمات: طابقين، 800 م²
- المباني السكنية: متوسط 600 م² لكل مبنى
- مبنى الصيانة: طابق واحد، 400 م²

💡 المرافق والخدمات:
• أنظمة التكييف: 95% تعمل بكفاءة
• أنظمة الإضاءة: 98% تعمل بشكل طبيعي
• أنظمة الأمان: 100% فعالة
• شبكات المياه: 92% في حالة جيدة

📊 استهلاك الطاقة:
- الكهرباء: 45,000 كيلو واط/ساعة شهرياً
- المياه: 8,500 متر مكعب شهرياً
- التكييف: 60% من استهلاك الكهرباء

🎯 خطط التطوير:
- تحديث أنظمة التكييف في المبنى الرئيسي
- صيانة شاملة للمباني التي تحتاج إصلاح
- تحسين كفاءة استهلاك الطاقة
- إضافة مرافق جديدة حسب الحاجة
    """
    
    # تقرير إدارة الصيانة
    maintenance_content = f"""
🔧 تقرير إدارة الصيانة والأعطال

📅 تاريخ التقرير: {current_time}

📊 إحصائيات بلاغات الصيانة:
• إجمالي البلاغات: 45 بلاغ
• البلاغات المكتملة: 28 بلاغ
• البلاغات قيد التنفيذ: 12 بلاغ
• البلاغات المعلقة: 5 بلاغات

🔍 أنواع الأعطال:
- أعطال كهربائية: 15 بلاغ (33%)
- أعطال سباكة: 12 بلاغ (27%)
- أعطال تكييف: 10 بلاغات (22%)
- أعطال أخرى: 8 بلاغات (18%)

⏱️ أوقات الاستجابة:
• متوسط وقت الاستجابة: 2.5 ساعة
• أسرع استجابة: 30 دقيقة
• أبطأ استجابة: 8 ساعات
• الهدف المطلوب: أقل من 4 ساعات

🏢 توزيع البلاغات حسب المباني:
- المبنى الرئيسي: 18 بلاغ
- المباني السكنية: 15 بلاغ
- مبنى الخدمات: 8 بلاغات
- مبنى الصيانة: 4 بلاغات

👥 فريق الصيانة:
• عدد الفنيين: 8 فنيين
• التخصصات المتوفرة: كهرباء، سباكة، تكييف، عام
• ساعات العمل: 24/7 للطوارئ
• متوسط الخبرة: 5 سنوات

💰 تكاليف الصيانة:
• التكلفة الشهرية: 25,000 ريال
• تكلفة قطع الغيار: 15,000 ريال
• تكلفة العمالة: 10,000 ريال
• نسبة التوفير: 12% مقارنة بالعام الماضي

📈 مؤشرات الأداء:
✅ معدل إنجاز البلاغات: 85%
✅ رضا المستخدمين: 92%
✅ الالتزام بالمواعيد: 88%
⚠️ تكرار الأعطال: 8% (يحتاج تحسين)

🎯 خطط التحسين:
- تدريب الفنيين على التقنيات الحديثة
- تحديث أدوات ومعدات الصيانة
- تطوير نظام الصيانة الوقائية
- تحسين إدارة المخزون وقطع الغيار
    """
    
    # إنشاء التقارير
    results = []
    
    print("📊 إنشاء تقرير إدارة المشاريع...")
    results.append(create_test_report("إدارة المشاريع", projects_content, "تقرير_المشاريع_عربي.pdf"))
    
    print("🏢 إنشاء تقرير إدارة المباني...")
    results.append(create_test_report("إدارة المباني", buildings_content, "تقرير_المباني_عربي.pdf"))
    
    print("🔧 إنشاء تقرير إدارة الصيانة...")
    results.append(create_test_report("إدارة الصيانة", maintenance_content, "تقرير_الصيانة_عربي.pdf"))
    
    # النتائج
    print("\n" + "=" * 60)
    print("📋 ملخص النتائج:")
    
    success_count = sum(results)
    total_count = len(results)
    
    if success_count == total_count:
        print(f"🎉 تم إنشاء جميع التقارير بنجاح! ({success_count}/{total_count})")
        print("\n✅ الملفات المُنشأة:")
        print("   📄 تقرير_المشاريع_عربي.pdf")
        print("   📄 تقرير_المباني_عربي.pdf")
        print("   📄 تقرير_الصيانة_عربي.pdf")
        
        print("\n🔍 للتحقق من الإصلاح:")
        print("   1. افتح أي من الملفات المُنشأة")
        print("   2. تأكد من ظهور النص العربي بوضوح")
        print("   3. تحقق من عدم وجود رموز غريبة أو مربعات فارغة")
        
        # محاولة فتح أحد الملفات
        try:
            import subprocess
            import platform
            
            test_file = os.path.join(current_dir, "تقرير_المشاريع_عربي.pdf")
            system = platform.system()
            
            if system == "Windows":
                subprocess.run(['start', '', test_file], shell=True, check=False)
                print(f"\n📖 تم فتح الملف للمراجعة: {test_file}")
            elif system == "Darwin":  # macOS
                subprocess.run(['open', test_file], check=False)
                print(f"\n📖 تم فتح الملف للمراجعة: {test_file}")
            elif system == "Linux":
                subprocess.run(['xdg-open', test_file], check=False)
                print(f"\n📖 تم فتح الملف للمراجعة: {test_file}")
                
        except Exception as e:
            print(f"\n⚠️ لا يمكن فتح الملف تلقائياً: {e}")
        
        return True
    else:
        print(f"❌ فشل في إنشاء بعض التقارير ({success_count}/{total_count})")
        return False

if __name__ == "__main__":
    success = test_all_reports()
    
    if success:
        print("\n🎊 تم إصلاح مشكلة الخطوط العربية في جميع التقارير بنجاح!")
    else:
        print("\n💥 هناك مشاكل في بعض التقارير، يرجى مراجعة الأخطاء أعلاه.")
