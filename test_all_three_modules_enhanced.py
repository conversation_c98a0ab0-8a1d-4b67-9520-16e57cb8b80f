#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للوحدات الثلاث المحدثة
Comprehensive Test for All Three Enhanced Modules
"""

import os
import sys
import tempfile
import tkinter as tk
from tkinter import messagebox
from datetime import datetime

def test_projects_enhanced_report():
    """اختبار تقرير المشاريع المحسن"""
    print("🔍 اختبار تقرير المشاريع المحسن...")
    
    try:
        from simple_project_management import SimpleProjectManagementWindow
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء نافذة إدارة المشاريع
        projects_window = SimpleProjectManagementWindow(root, None, None)
        
        # بيانات مشاريع تجريبية
        test_projects_data = [
            {
                'name': 'مشروع المبنى الإداري الجديد',
                'project_type': 'إنشائي',
                'status': 'مكتمل',
                'progress_percentage': 100,
                'cost': 5000000
            },
            {
                'name': 'تطوير مختبرات الهندسة',
                'project_type': 'أكاديمي',
                'status': 'قيد التنفيذ',
                'progress_percentage': 75,
                'cost': 3200000
            },
            {
                'name': 'تحديث أنظمة التكييف',
                'project_type': 'صيانة',
                'status': 'مكتمل',
                'progress_percentage': 100,
                'cost': 1800000
            }
        ]
        
        # إنشاء تقرير PDF للمشاريع
        with tempfile.NamedTemporaryFile(suffix='_enhanced_projects_test.pdf', delete=False) as temp_file:
            projects_window.create_projects_pdf_report(test_projects_data, temp_file.name)
            
            if os.path.exists(temp_file.name):
                size = os.path.getsize(temp_file.name)
                print(f"✅ تقرير المشاريع: {size:,} بايت")
                root.destroy()
                return True, temp_file.name
            else:
                print("❌ فشل في إنشاء تقرير المشاريع")
                root.destroy()
                return False, None
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تقرير المشاريع: {e}")
        return False, None

def test_buildings_enhanced_report():
    """اختبار تقرير المباني المحسن"""
    print("🔍 اختبار تقرير المباني المحسن...")
    
    try:
        from buildings_management import BuildingsManagementWindow
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء نافذة إدارة المباني
        buildings_window = BuildingsManagementWindow(root, None, None)
        
        # بيانات مباني تجريبية
        test_buildings_data = [
            {
                'name': 'المبنى الإداري الرئيسي',
                'building_type': 'إداري',
                'location': 'الحرم الجامعي الرئيسي',
                'floors': 5,
                'area': 2500.0,
                'construction_year': 2015,
                'owner': 'الجامعة',
                'structural_condition': 'ممتاز'
            },
            {
                'name': 'مبنى كلية الهندسة',
                'building_type': 'أكاديمي',
                'location': 'الحرم الجامعي الشمالي',
                'floors': 4,
                'area': 3200.0,
                'construction_year': 2018,
                'owner': 'الجامعة',
                'structural_condition': 'جيد جداً'
            },
            {
                'name': 'مبنى المختبرات',
                'building_type': 'أكاديمي',
                'location': 'منطقة المختبرات',
                'floors': 3,
                'area': 1800.0,
                'construction_year': 2020,
                'owner': 'الجامعة',
                'structural_condition': 'ممتاز'
            }
        ]
        
        # إنشاء تقرير PDF للمباني
        with tempfile.NamedTemporaryFile(suffix='_enhanced_buildings_test.pdf', delete=False) as temp_file:
            buildings_window.create_buildings_pdf_report(test_buildings_data, temp_file.name)
            
            if os.path.exists(temp_file.name):
                size = os.path.getsize(temp_file.name)
                print(f"✅ تقرير المباني: {size:,} بايت")
                root.destroy()
                return True, temp_file.name
            else:
                print("❌ فشل في إنشاء تقرير المباني")
                root.destroy()
                return False, None
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تقرير المباني: {e}")
        return False, None

def test_maintenance_enhanced_report():
    """اختبار تقرير الصيانة المحسن"""
    print("🔍 اختبار تقرير الصيانة المحسن...")
    
    try:
        from maintenance_management import MaintenanceManagementWindow
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء نافذة إدارة الصيانة
        maintenance_window = MaintenanceManagementWindow(root, None, None)
        
        # بيانات صيانة تجريبية
        test_maintenance_data = [
            {
                'title': 'عطل في نظام التكييف',
                'description': 'توقف مكيف الهواء في القاعة الرئيسية',
                'building': 'المبنى الإداري',
                'priority': 'عالية',
                'status': 'قيد المعالجة',
                'reported_date': '2025-07-01',
                'assigned_to': 'فني التكييف',
                'notes': 'يحتاج إلى قطع غيار'
            },
            {
                'title': 'مشكلة في الإضاءة',
                'description': 'عدم عمل الإضاءة في الممر الشرقي',
                'building': 'مبنى الهندسة',
                'priority': 'متوسطة',
                'status': 'مكتمل',
                'reported_date': '2025-06-28',
                'assigned_to': 'فني الكهرباء',
                'notes': 'تم استبدال المصابيح'
            },
            {
                'title': 'تسريب في السباكة',
                'description': 'تسريب مياه في دورة المياه الطابق الثاني',
                'building': 'مبنى المختبرات',
                'priority': 'عالية',
                'status': 'جديد',
                'reported_date': '2025-07-02',
                'assigned_to': 'فني السباكة',
                'notes': 'يحتاج إلى تدخل عاجل'
            }
        ]
        
        # إنشاء تقرير PDF للصيانة
        with tempfile.NamedTemporaryFile(suffix='_enhanced_maintenance_test.pdf', delete=False) as temp_file:
            maintenance_window.create_maintenance_pdf_report(test_maintenance_data, temp_file.name)
            
            if os.path.exists(temp_file.name):
                size = os.path.getsize(temp_file.name)
                print(f"✅ تقرير الصيانة: {size:,} بايت")
                root.destroy()
                return True, temp_file.name
            else:
                print("❌ فشل في إنشاء تقرير الصيانة")
                root.destroy()
                return False, None
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تقرير الصيانة: {e}")
        return False, None

def check_landscape_features(file_path, module_name):
    """فحص ميزات التخطيط الأفقي في الملف"""
    features_found = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # فحص الميزات المطلوبة
        if 'landscape(A4)' in content:
            features_found.append("✅ تخطيط أفقي")
        else:
            features_found.append("❌ تخطيط أفقي")
            
        if 'Arabic-Bold' in content or 'Arabic-Regular' in content:
            features_found.append("✅ دعم الخطوط العربية")
        else:
            features_found.append("❌ دعم الخطوط العربية")
            
        if 'fontSize=24' in content or 'fontSize=16' in content:
            features_found.append("✅ خطوط أكبر")
        else:
            features_found.append("❌ خطوط أكبر")
            
        if 'Color(0.12, 0.23, 0.58)' in content:
            features_found.append("✅ ألوان احترافية")
        else:
            features_found.append("❌ ألوان احترافية")
            
        if 'colWidths' in content:
            features_found.append("✅ أعمدة متناسبة")
        else:
            features_found.append("❌ أعمدة متناسبة")
            
        if 'auto_print_pdf' in content:
            features_found.append("✅ طباعة تلقائية")
        else:
            features_found.append("❌ طباعة تلقائية")
            
    except Exception as e:
        features_found.append(f"❌ خطأ في فحص الملف: {e}")
    
    return features_found

def main():
    """الدالة الرئيسية"""
    print("=" * 80)
    print("🎉 اختبار شامل للوحدات الثلاث المحدثة")
    print("=" * 80)
    print("📋 الوحدات المختبرة:")
    print("   1. 📊 إدارة المشاريع (simple_project_management.py)")
    print("   2. 🏢 إدارة المباني (buildings_management.py)")
    print("   3. 🔧 إدارة الصيانة (maintenance_management.py)")
    print("=" * 80)
    
    # تشغيل الاختبارات
    tests = [
        ("تقرير المشاريع المحسن", test_projects_enhanced_report),
        ("تقرير المباني المحسن", test_buildings_enhanced_report),
        ("تقرير الصيانة المحسن", test_maintenance_enhanced_report)
    ]
    
    results = []
    pdf_files = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        result, path = test_func()
        if path:
            pdf_files.append((test_name, path))
        results.append((test_name, result))
    
    # فحص الميزات في الملفات المصدرية
    print("\n" + "=" * 80)
    print("🔍 فحص الميزات في الملفات المصدرية:")
    print("=" * 80)
    
    modules_to_check = [
        ("simple_project_management.py", "إدارة المشاريع"),
        ("buildings_management.py", "إدارة المباني"),
        ("maintenance_management.py", "إدارة الصيانة")
    ]
    
    for file_path, module_name in modules_to_check:
        print(f"\n📄 {module_name} ({file_path}):")
        features = check_landscape_features(file_path, module_name)
        for feature in features:
            print(f"   {feature}")
    
    # عرض النتائج
    print("\n" + "=" * 80)
    print("📋 نتائج الاختبار الشامل:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        if result:
            print(f"✅ {test_name}: نجح")
            passed += 1
        else:
            print(f"❌ {test_name}: فشل")
    
    print(f"\n📊 الإحصائيات النهائية: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت بامتياز!")
        print("✅ جميع الوحدات الثلاث تحتوي على التحسينات الاحترافية")
        print("✅ التقارير الأفقية المحسنة جاهزة للاستخدام")
        print("✅ دعم كامل للعربية مفعل في جميع الوحدات")
        print("✅ تصميم احترافي مطبق على جميع التقارير")
    elif passed >= 2:
        print("\n✅ معظم الاختبارات نجحت!")
        print("✅ الميزات الأساسية تعمل بشكل ممتاز")
    else:
        print("\n⚠️ بعض الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    if pdf_files:
        print(f"\n📄 ملفات PDF المنشأة للمعاينة:")
        for i, (name, path) in enumerate(pdf_files, 1):
            print(f"   {i}. {name}: {path}")
    
    print(f"\n🕐 تاريخ الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🏗️ نظام إدارة أعمال الإدارة الهندسية - الإصدار 2.2")
    print("=" * 80)

if __name__ == "__main__":
    main()
