#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إصلاح مشكلة الخطوط العربية في التقارير
Test Arabic fonts fix in reports
"""

import sys
import os
import tkinter as tk
from datetime import datetime

# إضافة المسار الحالي لـ sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_arabic_fonts():
    """اختبار دعم الخطوط العربية"""
    print("🔍 بدء اختبار دعم الخطوط العربية...")
    
    try:
        # استيراد المكتبات المطلوبة
        from reports_management import ReportsManagementWindow
        from maintenance_management import MaintenanceManagementWindow
        from buildings_management import BuildingsManagementWindow
        from database_manager import DatabaseManager
        from auth_manager import AuthManager
        
        print("✅ تم استيراد جميع المكتبات بنجاح")
        
        # إنشاء نافذة رئيسية مؤقتة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        # إنشاء مدير قاعدة البيانات والمصادقة
        db_manager = DatabaseManager()
        auth_manager = AuthManager(db_manager)
        
        print("✅ تم إنشاء مديري قاعدة البيانات والمصادقة")
        
        # اختبار نافذة التقارير
        print("\n📊 اختبار نافذة التقارير:")
        reports_window = ReportsManagementWindow(root, db_manager, auth_manager)
        
        # اختبار إعداد الخطوط العربية
        arabic_support = reports_window.setup_arabic_pdf_support()
        if arabic_support:
            print("✅ تم تسجيل الخطوط العربية بنجاح في نافذة التقارير")
            if hasattr(reports_window, 'registered_fonts'):
                for font_name in reports_window.registered_fonts.keys():
                    print(f"   📋 خط مسجل: {font_name}")
        else:
            print("⚠️ سيتم استخدام الخطوط الافتراضية في نافذة التقارير")
        
        # اختبار إنشاء الأنماط
        styles = reports_window.create_pdf_styles()
        print(f"✅ تم إنشاء {len(styles)} نمط احترافي")
        
        # اختبار نافذة الصيانة
        print("\n🔧 اختبار نافذة الصيانة:")
        maintenance_window = MaintenanceManagementWindow(root, db_manager, auth_manager)
        
        arabic_support_maintenance = maintenance_window.setup_arabic_pdf_support()
        if arabic_support_maintenance:
            print("✅ تم تسجيل الخطوط العربية بنجاح في نافذة الصيانة")
            if hasattr(maintenance_window, 'registered_fonts'):
                for font_name in maintenance_window.registered_fonts.keys():
                    print(f"   📋 خط مسجل: {font_name}")
        else:
            print("⚠️ سيتم استخدام الخطوط الافتراضية في نافذة الصيانة")
        
        # اختبار نافذة المباني
        print("\n🏢 اختبار نافذة المباني:")
        buildings_window = BuildingsManagementWindow(root, db_manager, auth_manager)
        
        arabic_support_buildings = buildings_window.setup_arabic_pdf_support()
        if arabic_support_buildings:
            print("✅ تم تسجيل الخطوط العربية بنجاح في نافذة المباني")
            if hasattr(buildings_window, 'registered_fonts'):
                for font_name in buildings_window.registered_fonts.keys():
                    print(f"   📋 خط مسجل: {font_name}")
        else:
            print("⚠️ سيتم استخدام الخطوط الافتراضية في نافذة المباني")
        
        # اختبار إنشاء تقرير تجريبي
        print("\n📄 اختبار إنشاء تقرير تجريبي:")
        test_content = """
تقرير اختبار الخطوط العربية

📊 إحصائيات عامة:
• عدد المشاريع: 15
• عدد المباني: 8
• عدد بلاغات الصيانة: 23

🏗️ تفاصيل المشاريع:
- مشروع تطوير المكاتب الإدارية
- مشروع صيانة المرافق العامة
- مشروع تحديث أنظمة التكييف

🔧 حالة الصيانة:
✅ مكتملة: 15 بلاغ
⏳ قيد التنفيذ: 5 بلاغات
❌ معلقة: 3 بلاغات

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d - %H:%M")
        
        # محاولة إنشاء ملف PDF تجريبي
        try:
            test_file_path = os.path.join(current_dir, "test_arabic_report.pdf")
            reports_window.create_pdf_report(test_content, test_file_path, "تقرير اختبار الخطوط العربية")
            print(f"✅ تم إنشاء تقرير PDF تجريبي: {test_file_path}")
            
            # التحقق من وجود الملف
            if os.path.exists(test_file_path):
                file_size = os.path.getsize(test_file_path)
                print(f"📁 حجم الملف: {file_size} بايت")
                
                if file_size > 1000:  # إذا كان حجم الملف معقول
                    print("✅ تم إنشاء التقرير بنجاح ويبدو أنه يحتوي على محتوى صحيح")
                else:
                    print("⚠️ حجم الملف صغير، قد تكون هناك مشكلة في المحتوى")
            else:
                print("❌ لم يتم إنشاء الملف")
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير التجريبي: {e}")
        
        # تنظيف الموارد
        root.destroy()
        
        print("\n🎉 انتهى اختبار الخطوط العربية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الخطوط العربية: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_system_fonts():
    """فحص الخطوط المتوفرة في النظام"""
    print("\n🔍 فحص الخطوط المتوفرة في النظام:")
    
    import platform
    import os
    
    system = platform.system()
    print(f"نظام التشغيل: {system}")
    
    if system == "Windows":
        fonts_dir = "C:/Windows/Fonts"
        arabic_fonts = [
            "arial.ttf", "arialbd.ttf", "ariali.ttf",
            "tahoma.ttf", "tahomabd.ttf",
            "calibri.ttf", "calibrib.ttf", "calibrii.ttf",
            "segoeui.ttf", "segoeuib.ttf"
        ]
    else:
        fonts_dir = "/usr/share/fonts/truetype/dejavu"
        arabic_fonts = [
            "DejaVuSans.ttf", "DejaVuSans-Bold.ttf", "DejaVuSans-Oblique.ttf"
        ]
    
    print(f"مجلد الخطوط: {fonts_dir}")
    
    if os.path.exists(fonts_dir):
        available_fonts = []
        for font in arabic_fonts:
            font_path = os.path.join(fonts_dir, font)
            if os.path.exists(font_path):
                available_fonts.append(font)
                print(f"✅ متوفر: {font}")
            else:
                print(f"❌ غير متوفر: {font}")
        
        print(f"\nإجمالي الخطوط المتوفرة: {len(available_fonts)} من {len(arabic_fonts)}")
        return len(available_fonts) > 0
    else:
        print(f"❌ مجلد الخطوط غير موجود: {fonts_dir}")
        return False

if __name__ == "__main__":
    print("🚀 بدء اختبار إصلاح الخطوط العربية")
    print("=" * 50)
    
    # فحص الخطوط المتوفرة في النظام
    fonts_available = check_system_fonts()
    
    if fonts_available:
        print("\n✅ تم العثور على خطوط مناسبة في النظام")
    else:
        print("\n⚠️ لم يتم العثور على خطوط عربية، سيتم استخدام الخطوط الافتراضية")
    
    # اختبار دعم الخطوط العربية
    success = test_arabic_fonts()
    
    if success:
        print("\n🎉 تم إصلاح مشكلة الخطوط العربية بنجاح!")
        print("يمكنك الآن إنشاء تقارير PDF بالعربية بشكل صحيح.")
    else:
        print("\n❌ فشل في إصلاح مشكلة الخطوط العربية")
        print("يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة.")
