#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الخطوط العربية فقط
Test Arabic fonts only
"""

import sys
import os
from datetime import datetime

# إضافة المسار الحالي لـ sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_reportlab_arabic():
    """اختبار دعم الخطوط العربية في reportlab"""
    print("🔍 اختبار دعم الخطوط العربية في reportlab...")
    
    try:
        # استيراد مكتبات reportlab
        from reportlab.lib.pagesizes import landscape, A4
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from reportlab.lib.units import cm
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER, TA_RIGHT
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        import platform
        
        print("✅ تم استيراد مكتبات reportlab بنجاح")
        
        # إعداد الخطوط العربية
        registered_fonts = {}
        
        if platform.system() == "Windows":
            font_paths = {
                'Arabic-Bold': [
                    "C:/Windows/Fonts/arialbd.ttf",
                    "C:/Windows/Fonts/tahomabd.ttf",
                    "C:/Windows/Fonts/calibrib.ttf"
                ],
                'Arabic-Regular': [
                    "C:/Windows/Fonts/arial.ttf",
                    "C:/Windows/Fonts/tahoma.ttf",
                    "C:/Windows/Fonts/calibri.ttf",
                    "C:/Windows/Fonts/segoeui.ttf"
                ]
            }
        else:
            font_paths = {
                'Arabic-Bold': [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
                ],
                'Arabic-Regular': [
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
                ]
            }
        
        # تسجيل الخطوط
        for font_name, paths in font_paths.items():
            for font_path in paths:
                if os.path.exists(font_path):
                    try:
                        pdfmetrics.registerFont(TTFont(font_name, font_path))
                        registered_fonts[font_name] = font_name
                        print(f"✅ تم تسجيل الخط: {font_name} من {font_path}")
                        break
                    except Exception as e:
                        print(f"⚠️ فشل في تسجيل {font_name}: {e}")
                        continue
        
        if not registered_fonts:
            print("⚠️ لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")
            registered_fonts = {
                'Arabic-Bold': 'Helvetica-Bold',
                'Arabic-Regular': 'Helvetica'
            }
        
        # إنشاء ملف PDF تجريبي
        test_file_path = os.path.join(current_dir, "test_arabic_fonts.pdf")
        
        doc = SimpleDocTemplate(
            test_file_path,
            pagesize=landscape(A4),
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm,
            title="اختبار الخطوط العربية",
            author="نظام إدارة أعمال الإدارة الهندسية"
        )
        
        # إنشاء الأنماط
        styles = getSampleStyleSheet()
        
        # تحديد الخطوط
        title_font = 'Arabic-Bold' if 'Arabic-Bold' in registered_fonts else 'Arabic-Regular'
        normal_font = 'Arabic-Regular' if 'Arabic-Regular' in registered_fonts else 'Arabic-Bold'
        
        # نمط العنوان
        title_style = ParagraphStyle(
            'ArabicTitle',
            parent=styles['Title'],
            fontName=title_font,
            fontSize=24,
            spaceAfter=25,
            alignment=TA_CENTER,
            textColor=colors.Color(0.12, 0.23, 0.54)
        )
        
        # نمط النص العادي
        normal_style = ParagraphStyle(
            'ArabicNormal',
            parent=styles['Normal'],
            fontName=normal_font,
            fontSize=12,
            spaceAfter=10,
            alignment=TA_RIGHT,
            textColor=colors.black
        )
        
        # إنشاء المحتوى
        story = []
        
        # العنوان
        story.append(Paragraph("🏗️ اختبار الخطوط العربية في التقارير 🏗️", title_style))
        story.append(Spacer(1, 20))
        
        # النص العربي
        arabic_text = """
هذا اختبار لعرض النص العربي في ملفات PDF باستخدام الخطوط المحسنة.

📊 إحصائيات النظام:
• عدد المشاريع: 15 مشروع
• عدد المباني: 8 مباني  
• عدد بلاغات الصيانة: 23 بلاغ

🏗️ أنواع المشاريع:
- مشاريع البناء والتشييد
- مشاريع الصيانة والتطوير
- مشاريع التحديث والتجديد

🔧 حالات الصيانة:
✅ مكتملة: 15 بلاغ
⏳ قيد التنفيذ: 5 بلاغات
❌ معلقة: 3 بلاغات

📅 تاريخ إنشاء التقرير: """ + datetime.now().strftime("%Y/%m/%d - %H:%M") + """

🎯 الهدف من هذا الاختبار:
التأكد من أن النص العربي يظهر بشكل صحيح وواضح في ملفات PDF المُصدرة من النظام.
يجب أن تكون جميع الأحرف العربية مقروءة ومفهومة بدون أي رموز غريبة أو مربعات فارغة.

✨ الخطوط المستخدمة:
- خط العنوان: """ + title_font + """
- خط النص العادي: """ + normal_font + """

🔍 إذا كنت تقرأ هذا النص بوضوح، فقد تم إصلاح مشكلة الخطوط العربية بنجاح!
        """
        
        # إضافة النص إلى التقرير
        for line in arabic_text.strip().split('\n'):
            if line.strip():
                story.append(Paragraph(line.strip(), normal_style))
                story.append(Spacer(1, 5))
        
        # بناء المستند
        doc.build(story)
        
        # التحقق من إنشاء الملف
        if os.path.exists(test_file_path):
            file_size = os.path.getsize(test_file_path)
            print(f"✅ تم إنشاء ملف PDF تجريبي: {test_file_path}")
            print(f"📁 حجم الملف: {file_size} بايت")
            
            if file_size > 5000:  # حجم معقول للملف
                print("✅ الملف يحتوي على محتوى صحيح")
                
                # محاولة فتح الملف
                try:
                    import subprocess
                    import platform
                    
                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['start', '', test_file_path], shell=True, check=False)
                        print("📖 تم فتح الملف للمراجعة")
                    elif system == "Darwin":  # macOS
                        subprocess.run(['open', test_file_path], check=False)
                        print("📖 تم فتح الملف للمراجعة")
                    elif system == "Linux":
                        subprocess.run(['xdg-open', test_file_path], check=False)
                        print("📖 تم فتح الملف للمراجعة")
                        
                except Exception as e:
                    print(f"⚠️ لا يمكن فتح الملف تلقائياً: {e}")
                
                return True
            else:
                print("⚠️ حجم الملف صغير، قد تكون هناك مشكلة")
                return False
        else:
            print("❌ لم يتم إنشاء الملف")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الخطوط العربية: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_font_files():
    """فحص ملفات الخطوط في النظام"""
    print("\n🔍 فحص ملفات الخطوط في النظام:")
    
    import platform
    
    system = platform.system()
    print(f"نظام التشغيل: {system}")
    
    if system == "Windows":
        fonts_dir = "C:/Windows/Fonts"
        test_fonts = [
            "arial.ttf", "arialbd.ttf", "ariali.ttf",
            "tahoma.ttf", "tahomabd.ttf",
            "calibri.ttf", "calibrib.ttf", "calibrii.ttf",
            "segoeui.ttf"
        ]
    else:
        fonts_dir = "/usr/share/fonts/truetype/dejavu"
        test_fonts = [
            "DejaVuSans.ttf", "DejaVuSans-Bold.ttf", "DejaVuSans-Oblique.ttf"
        ]
    
    print(f"مجلد الخطوط: {fonts_dir}")
    
    if os.path.exists(fonts_dir):
        available_count = 0
        for font in test_fonts:
            font_path = os.path.join(fonts_dir, font)
            if os.path.exists(font_path):
                file_size = os.path.getsize(font_path)
                print(f"✅ {font} - {file_size:,} بايت")
                available_count += 1
            else:
                print(f"❌ {font} - غير موجود")
        
        print(f"\nالخطوط المتوفرة: {available_count}/{len(test_fonts)}")
        return available_count > 0
    else:
        print(f"❌ مجلد الخطوط غير موجود: {fonts_dir}")
        return False

if __name__ == "__main__":
    print("🚀 اختبار الخطوط العربية في التقارير")
    print("=" * 50)
    
    # فحص ملفات الخطوط
    fonts_available = check_font_files()
    
    if fonts_available:
        print("\n✅ تم العثور على خطوط مناسبة")
        
        # اختبار إنشاء PDF بالخطوط العربية
        success = test_reportlab_arabic()
        
        if success:
            print("\n🎉 تم إصلاح مشكلة الخطوط العربية بنجاح!")
            print("📖 يرجى فتح الملف المُنشأ والتحقق من ظهور النص العربي بشكل صحيح.")
            print("📁 الملف: test_arabic_fonts.pdf")
        else:
            print("\n❌ فشل في إنشاء التقرير التجريبي")
    else:
        print("\n⚠️ لم يتم العثور على خطوط مناسبة في النظام")
        print("سيتم استخدام الخطوط الافتراضية")
