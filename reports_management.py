"""
وحدة إدارة التقارير
Reports Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from datetime import datetime
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib
import os
matplotlib.use('TkAgg')

# استيراد مكتبات PDF
try:
    from reportlab.lib.pagesizes import A4, letter, landscape
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from reportlab.platypus.flowables import Image
    import io
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("تحذير: مكتبة reportlab غير متوفرة. ميزة تصدير PDF معطلة.")

class ReportsManagementWindow:
    """نافذة إدارة التقارير"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")

    def show(self):
        """عرض نافذة التقارير"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("📊 لوحة التحكم والتقارير")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة التقارير"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        title_label = ttk_bs.Label(
            header_frame,
            text="📊 لوحة التحكم والتقارير 📊",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack()

        # إطار الإحصائيات السريعة
        stats_frame = ttk_bs.LabelFrame(
            self.window,
            text="📈 الإحصائيات السريعة",
            padding=20,
            bootstyle="info"
        )
        stats_frame.pack(fill=tk.X, padx=15, pady=10)

        # إنشاء بطاقات الإحصائيات
        stats_container = ttk_bs.Frame(stats_frame)
        stats_container.pack(fill=tk.X)

        self.create_stat_card(stats_container, "🏗️ المشاريع النشطة", "12", "success", 0)
        self.create_stat_card(stats_container, "🏢 المباني", "8", "info", 1)
        self.create_stat_card(stats_container, "🔧 بلاغات الصيانة", "25", "warning", 2)
        self.create_stat_card(stats_container, "✅ المهام المكتملة", "45", "primary", 3)

        # إطار التقارير والرسوم البيانية
        reports_frame = ttk_bs.LabelFrame(
            self.window,
            text="📊 التقارير والرسوم البيانية",
            padding=20,
            bootstyle="secondary"
        )
        reports_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Notebook للتبويبات
        notebook = ttk_bs.Notebook(reports_frame, bootstyle="info")
        notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب المشاريع
        projects_tab = ttk_bs.Frame(notebook)
        notebook.add(projects_tab, text="📈 تقارير المشاريع")
        self.create_projects_chart(projects_tab)

        # تبويب الصيانة
        maintenance_tab = ttk_bs.Frame(notebook)
        notebook.add(maintenance_tab, text="🔧 تقارير الصيانة")
        self.create_maintenance_chart(maintenance_tab)

        # تبويب المباني
        buildings_tab = ttk_bs.Frame(notebook)
        notebook.add(buildings_tab, text="🏢 تقارير المباني")
        self.create_buildings_chart(buildings_tab)

        # تبويب التقارير المفصلة
        detailed_tab = ttk_bs.Frame(notebook)
        notebook.add(detailed_tab, text="📋 تقارير مفصلة")
        self.create_detailed_reports(detailed_tab)

    def create_stat_card(self, parent, title, value, style, column):
        """إنشاء بطاقة إحصائية"""
        card_frame = ttk_bs.LabelFrame(
            parent,
            text=title,
            padding=15,
            bootstyle=style
        )
        card_frame.grid(row=0, column=column, padx=10, pady=5, sticky="ew")
        parent.grid_columnconfigure(column, weight=1)

        value_label = ttk_bs.Label(
            card_frame,
            text=value,
            bootstyle=style,
            foreground="#1e3a8a"
        )
        value_label.pack()

    def create_projects_chart(self, parent):
        """إنشاء رسم بياني للمشاريع"""
        chart_frame = ttk_bs.Frame(parent, padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # رسم بياني دائري لحالة المشاريع
        project_status = ['مكتمل', 'قيد التنفيذ', 'مؤجل', 'ملغي']
        project_counts = [8, 12, 3, 2]
        colors = ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
        
        ax1.pie(project_counts, labels=project_status, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('توزيع المشاريع حسب الحالة', fontsize=14, fontweight='bold')

        # رسم بياني عمودي للمشاريع حسب النوع
        project_types = ['سكني', 'تجاري', 'إداري', 'صناعي']
        type_counts = [15, 8, 12, 5]
        
        ax2.bar(project_types, type_counts, color=['#007bff', '#28a745', '#ffc107', '#dc3545'])
        ax2.set_title('المشاريع حسب النوع', fontsize=14, fontweight='bold')
        ax2.set_ylabel('عدد المشاريع')

        plt.tight_layout()

        # إدراج الرسم في النافذة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_maintenance_chart(self, parent):
        """إنشاء رسم بياني للصيانة"""
        chart_frame = ttk_bs.Frame(parent, padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # رسم بياني دائري لحالة بلاغات الصيانة
        maintenance_status = ['مكتمل', 'قيد التنفيذ', 'جديد', 'مؤجل']
        maintenance_counts = [15, 8, 12, 5]
        colors = ['#28a745', '#ffc107', '#007bff', '#fd7e14']
        
        ax1.pie(maintenance_counts, labels=maintenance_status, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('توزيع بلاغات الصيانة حسب الحالة', fontsize=14, fontweight='bold')

        # رسم بياني عمودي لأنواع الأعطال
        fault_types = ['كهرباء', 'سباكة', 'تكييف', 'أخرى']
        fault_counts = [12, 8, 15, 5]
        
        ax2.bar(fault_types, fault_counts, color=['#ffc107', '#007bff', '#28a745', '#dc3545'])
        ax2.set_title('الأعطال حسب النوع', fontsize=14, fontweight='bold')
        ax2.set_ylabel('عدد البلاغات')

        plt.tight_layout()

        # إدراج الرسم في النافذة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_buildings_chart(self, parent):
        """إنشاء رسم بياني للمباني"""
        chart_frame = ttk_bs.Frame(parent, padding=10)
        chart_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء الرسم البياني
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # رسم بياني دائري لأنواع المباني
        building_types = ['إداري', 'أكاديمي', 'خدمي', 'تقني']
        building_counts = [3, 2, 2, 1]
        colors = ['#007bff', '#28a745', '#ffc107', '#fd7e14']
        
        ax1.pie(building_counts, labels=building_types, colors=colors, autopct='%1.1f%%', startangle=90)
        ax1.set_title('توزيع المباني حسب النوع', fontsize=14, fontweight='bold')

        # رسم بياني عمودي لحالة المباني
        building_status = ['نشط', 'قيد الصيانة', 'مغلق مؤقتاً']
        status_counts = [6, 1, 1]
        
        ax2.bar(building_status, status_counts, color=['#28a745', '#ffc107', '#dc3545'])
        ax2.set_title('المباني حسب الحالة', fontsize=14, fontweight='bold')
        ax2.set_ylabel('عدد المباني')

        plt.tight_layout()

        # إدراج الرسم في النافذة
        canvas = FigureCanvasTkAgg(fig, chart_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def create_detailed_reports(self, parent):
        """إنشاء قسم التقارير المفصلة"""
        reports_frame = ttk_bs.Frame(parent, padding=20)
        reports_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان القسم
        title_label = ttk_bs.Label(
            reports_frame,
            text="📋 التقارير المفصلة",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(pady=(0, 20))

        # أزرار التقارير
        buttons_frame = ttk_bs.Frame(reports_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        # الصف الأول من الأزرار
        row1_frame = ttk_bs.Frame(buttons_frame)
        row1_frame.pack(fill=tk.X, pady=5)

        ttk_bs.Button(
            row1_frame,
            text="📊 تقرير شامل للمشاريع",
            command=self.generate_projects_report,
            bootstyle="primary",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row1_frame,
            text="🔧 تقرير الصيانة الشهري",
            command=self.generate_maintenance_report,
            bootstyle="warning",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row1_frame,
            text="🏢 تقرير حالة المباني",
            command=self.generate_buildings_report,
            bootstyle="info",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        # الصف الثاني من الأزرار
        row2_frame = ttk_bs.Frame(buttons_frame)
        row2_frame.pack(fill=tk.X, pady=5)

        ttk_bs.Button(
            row2_frame,
            text="💰 التقرير المالي",
            command=self.generate_financial_report,
            bootstyle="success",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row2_frame,
            text="📈 تقرير الأداء",
            command=self.generate_performance_report,
            bootstyle="secondary",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row2_frame,
            text="📅 التقرير السنوي",
            command=self.generate_annual_report,
            bootstyle="danger",
            width=25).pack(side=tk.LEFT, padx=10, ipady=10)

        # الصف الثالث - أزرار الطباعة
        row3_frame = ttk_bs.Frame(buttons_frame)
        row3_frame.pack(fill=tk.X, pady=15)

        ttk_bs.Button(
            row3_frame,
            text="🖨️ طباعة التقرير الحالي",
            command=self.print_current_report,
            bootstyle="secondary",
            width=30).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row3_frame,
            text="💾 حفظ التقرير كملف",
            command=self.save_report_to_file,
            bootstyle="secondary",
            width=30).pack(side=tk.LEFT, padx=10, ipady=10)

        # الصف الرابع - أزرار تصدير PDF
        row4_frame = ttk_bs.Frame(buttons_frame)
        row4_frame.pack(fill=tk.X, pady=15)

        ttk_bs.Button(
            row4_frame,
            text="📄 تصدير التقرير الحالي إلى PDF",
            command=self.export_current_report_to_pdf,
            bootstyle="success",
            width=35).pack(side=tk.LEFT, padx=10, ipady=10)

        ttk_bs.Button(
            row4_frame,
            text="📊 تصدير جميع التقارير إلى PDF",
            command=self.export_all_reports_to_pdf,
            bootstyle="info",
            width=35).pack(side=tk.LEFT, padx=10, ipady=10)

        # منطقة معاينة التقرير
        preview_frame = ttk_bs.LabelFrame(
            reports_frame,
            text="👁️ معاينة التقرير",
            padding=15,
            bootstyle="secondary"
        )
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=20)

        self.report_text = tk.Text(
            preview_frame,
            height=15,
            width=80,
            wrap=tk.WORD
        )
        self.report_text.pack(fill=tk.BOTH, expand=True)

        # شريط التمرير للنص
        scrollbar = tk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.report_text.yview)
        self.report_text.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إضافة نص ترحيبي
        welcome_text = """
📋 مرحباً بك في قسم التقارير المفصلة!

يمكنك من هنا إنشاء تقارير مفصلة عن:
• المشاريع وحالتها
• بلاغات الصيانة والأعطال
• حالة المباني والمرافق
• التقارير المالية
• تقارير الأداء
• التقارير السنوية

اختر نوع التقرير المطلوب من الأزرار أعلاه لعرض التفاصيل هنا.
        """
        self.report_text.insert(tk.END, welcome_text)

    def generate_projects_report(self):
        """إنشاء تقرير المشاريع"""
        report = """
📊 تقرير شامل للمشاريع
====================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

📈 ملخص المشاريع:
• إجمالي المشاريع: 25 مشروع
• المشاريع المكتملة: 8 مشاريع (32%)
• المشاريع قيد التنفيذ: 12 مشروع (48%)
• المشاريع المؤجلة: 3 مشاريع (12%)
• المشاريع الملغية: 2 مشروع (8%)

🏗️ المشاريع حسب النوع:
• مشاريع سكنية: 15 مشروع
• مشاريع تجارية: 8 مشاريع
• مشاريع إدارية: 12 مشروع
• مشاريع صناعية: 5 مشاريع

💰 الميزانية الإجمالية:
• إجمالي الميزانية: 50,000,000 ريال
• المبلغ المنفق: 32,000,000 ريال (64%)
• المبلغ المتبقي: 18,000,000 ريال (36%)

📊 أداء المشاريع:
• متوسط نسبة الإنجاز: 68%
• المشاريع في الموعد: 18 مشروع
• المشاريع المتأخرة: 4 مشاريع
• المشاريع المبكرة: 3 مشاريع

🎯 التوصيات:
• متابعة المشاريع المتأخرة
• تحسين التخطيط الزمني
• زيادة الرقابة على الجودة
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_maintenance_report(self):
        """إنشاء تقرير الصيانة"""
        report = """
🔧 تقرير الصيانة الشهري
=====================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

📈 ملخص بلاغات الصيانة:
• إجمالي البلاغات: 40 بلاغ
• البلاغات المكتملة: 15 بلاغ (37.5%)
• البلاغات قيد التنفيذ: 8 بلاغات (20%)
• البلاغات الجديدة: 12 بلاغ (30%)
• البلاغات المؤجلة: 5 بلاغات (12.5%)

🔧 الأعطال حسب النوع:
• أعطال كهربائية: 12 بلاغ
• أعطال السباكة: 8 بلاغات
• أعطال التكييف: 15 بلاغ
• أعطال أخرى: 5 بلاغات

⚡ الأولوية:
• أولوية عالية: 10 بلاغات
• أولوية متوسطة: 20 بلاغ
• أولوية منخفضة: 10 بلاغات

🏢 البلاغات حسب المبنى:
• المبنى الإداري: 15 بلاغ
• مبنى الهندسة: 12 بلاغ
• مبنى المختبرات: 8 بلاغات
• مباني أخرى: 5 بلاغات

⏱️ متوسط وقت الاستجابة:
• الأولوية العالية: 2 ساعة
• الأولوية المتوسطة: 8 ساعات
• الأولوية المنخفضة: 24 ساعة

🎯 التوصيات:
• تحسين وقت الاستجابة
• زيادة فريق الصيانة
• الصيانة الوقائية المنتظمة
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_buildings_report(self):
        """إنشاء تقرير المباني"""
        report = """
🏢 تقرير حالة المباني
==================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🏗️ ملخص المباني:
• إجمالي المباني: 8 مباني
• المباني النشطة: 6 مباني (75%)
• المباني قيد الصيانة: 1 مبنى (12.5%)
• المباني المغلقة مؤقتاً: 1 مبنى (12.5%)

🏢 المباني حسب النوع:
• مباني إدارية: 3 مباني
• مباني أكاديمية: 2 مبنى
• مباني خدمية: 2 مبنى
• مباني تقنية: 1 مبنى

📐 المساحات:
• إجمالي المساحة: 12,500 م²
• متوسط المساحة: 1,562 م²
• أكبر مبنى: 2,500 م²
• أصغر مبنى: 800 م²

📅 أعمار المباني:
• مباني حديثة (أقل من 5 سنوات): 4 مباني
• مباني متوسطة (5-10 سنوات): 3 مباني
• مباني قديمة (أكثر من 10 سنوات): 1 مبنى

🔧 حالة الصيانة:
• مباني بحالة ممتازة: 5 مباني
• مباني بحالة جيدة: 2 مبنى
• مباني تحتاج صيانة: 1 مبنى

💡 استهلاك الطاقة:
• متوسط الاستهلاك الشهري: 45,000 كيلو واط
• تكلفة الكهرباء الشهرية: 18,000 ريال
• توفير الطاقة المحقق: 15%

🎯 التوصيات:
• تحديث أنظمة التكييف
• تحسين العزل الحراري
• استخدام الطاقة المتجددة
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_financial_report(self):
        """إنشاء التقرير المالي"""
        report = """
💰 التقرير المالي
===============

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

💵 الميزانية العامة:
• إجمالي الميزانية: 75,000,000 ريال
• المبلغ المنفق: 48,500,000 ريال (64.7%)
• المبلغ المتبقي: 26,500,000 ريال (35.3%)

🏗️ مصروفات المشاريع:
• مشاريع البناء: 32,000,000 ريال
• مشاريع التطوير: 8,500,000 ريال
• مشاريع الصيانة: 3,000,000 ريال
• مشاريع أخرى: 5,000,000 ريال

🔧 مصروفات الصيانة:
• الصيانة الطارئة: 1,200,000 ريال
• الصيانة الوقائية: 800,000 ريال
• قطع الغيار: 600,000 ريال
• العمالة: 400,000 ريال

🏢 مصروفات التشغيل:
• الكهرباء: 216,000 ريال/سنوياً
• المياه: 84,000 ريال/سنوياً
• التنظيف: 120,000 ريال/سنوياً
• الأمن: 180,000 ريال/سنوياً

📊 التحليل المالي:
• معدل الإنفاق الشهري: 4,041,667 ريال
• توقع الإنفاق السنوي: 48,500,000 ريال
• نسبة التوفير: 8.5%
• العائد على الاستثمار: 12%

🎯 التوصيات المالية:
• تحسين إدارة التكاليف
• زيادة كفاءة الإنفاق
• البحث عن مصادر تمويل إضافية
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_performance_report(self):
        """إنشاء تقرير الأداء"""
        report = """
📈 تقرير الأداء
=============

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🎯 مؤشرات الأداء الرئيسية:
• نسبة إنجاز المشاريع: 68%
• نسبة المشاريع في الموعد: 72%
• رضا العملاء: 85%
• كفاءة استخدام الموارد: 78%

⏱️ الأداء الزمني:
• متوسط وقت إنجاز المشاريع: 8.5 شهر
• متوسط التأخير: 15 يوم
• نسبة المشاريع المبكرة: 12%
• نسبة المشاريع المتأخرة: 16%

💰 الأداء المالي:
• نسبة الالتزام بالميزانية: 92%
• متوسط توفير التكاليف: 8.5%
• العائد على الاستثمار: 12%
• كفاءة الإنفاق: 88%

🔧 أداء الصيانة:
• وقت الاستجابة للأعطال: 4 ساعات
• نسبة حل المشاكل من المرة الأولى: 78%
• رضا المستخدمين عن الصيانة: 82%
• تكلفة الصيانة لكل متر مربع: 24 ريال

👥 أداء الفريق:
• إنتاجية الفريق: 85%
• نسبة الحضور: 96%
• معدل دوران الموظفين: 5%
• رضا الموظفين: 88%

🏆 الإنجازات:
• إنجاز 8 مشاريع بنجاح
• توفير 4.2 مليون ريال
• تحسين كفاءة الطاقة بنسبة 15%
• حصول على شهادة الجودة ISO

🎯 مجالات التحسين:
• تقليل وقت إنجاز المشاريع
• تحسين التخطيط المسبق
• زيادة الاستثمار في التدريب
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def generate_annual_report(self):
        """إنشاء التقرير السنوي"""
        report = """
📅 التقرير السنوي 2024
===================

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🎯 ملخص تنفيذي:
تم خلال عام 2024 إنجاز العديد من المشاريع الهامة وتحقيق نتائج إيجابية
في جميع المجالات. شهد العام نمواً ملحوظاً في الأداء والكفاءة.

📊 الإنجازات الرئيسية:
• إنجاز 25 مشروع بقيمة 50 مليون ريال
• تحسين كفاءة الطاقة بنسبة 15%
• تقليل تكاليف الصيانة بنسبة 12%
• زيادة رضا العملاء إلى 85%

🏗️ المشاريع المنجزة:
• مشروع المبنى الإداري الجديد
• تطوير نظام إدارة المرافق
• تحديث أنظمة التكييف
• مشروع الطاقة الشمسية

💰 الأداء المالي:
• إجمالي الإيرادات: 65 مليون ريال
• إجمالي المصروفات: 48.5 مليون ريال
• صافي الربح: 16.5 مليون ريال
• نسبة الربحية: 25.4%

🔧 تطوير الصيانة:
• تطبيق نظام الصيانة الذكية
• تدريب فرق الصيانة
• تحديث المعدات والأدوات
• تحسين أوقات الاستجابة

🏢 تطوير المرافق:
• إضافة 3 مباني جديدة
• تحديث 5 مباني قائمة
• تحسين البنية التحتية
• تطوير المساحات الخضراء

👥 تطوير الموارد البشرية:
• توظيف 15 موظف جديد
• تدريب 50 موظف
• تحسين بيئة العمل
• زيادة الرواتب بنسبة 8%

🌱 الاستدامة والبيئة:
• تقليل استهلاك الطاقة بنسبة 15%
• تقليل استهلاك المياه بنسبة 10%
• إعادة تدوير 80% من النفايات
• زراعة 200 شجرة

🎯 خطط العام القادم:
• إنجاز 30 مشروع جديد
• زيادة الاستثمار في التقنية
• تطوير الموارد البشرية
• تحسين الاستدامة البيئية

📈 التوقعات:
• نمو الإيرادات بنسبة 20%
• تحسين الكفاءة بنسبة 15%
• زيادة رضا العملاء إلى 90%
• تقليل التكاليف بنسبة 10%
        """
        self.report_text.delete(1.0, tk.END)
        self.report_text.insert(tk.END, report)

    def print_current_report(self):
        """طباعة التقرير الحالي"""
        try:
            import tempfile
            import os
            
            # الحصول على محتوى التقرير
            report_content = self.report_text.get(1.0, tk.END)
            
            if not report_content.strip():
                messagebox.showwarning("تحذير", "لا يوجد تقرير لطباعته. يرجى إنشاء تقرير أولاً.")
                return
            
            # إنشاء ملف مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(report_content)
                temp_file_path = temp_file.name
            
            # فتح الملف للطباعة بطريقة آمنة
            try:
                import platform
                import subprocess

                system = platform.system()
                if system == "Windows":
                    # استخدام طريقة آمنة لفتح الملف
                    try:
                        # محاولة فتح الملف مع برنامج النصوص الافتراضي
                        subprocess.run(['start', '', temp_file_path], shell=True, check=False)
                    except:
                        # في حالة فشل الطريقة الأولى، استخدم os.startfile
                        try:
                            os.startfile(temp_file_path)
                        except:
                            # إذا فشلت جميع الطرق، أظهر رسالة للمستخدم
                            messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{temp_file_path}\nيرجى فتحه يدوياً للطباعة")
                            return
                elif system == "Darwin":  # macOS
                    subprocess.run(['open', temp_file_path], check=False)
                elif system == "Linux":
                    subprocess.run(['xdg-open', temp_file_path], check=False)
                else:
                    messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{temp_file_path}\nيرجى فتحه يدوياً للطباعة")
                    return

                messagebox.showinfo("نجح", "تم إرسال التقرير للطباعة")
            except Exception as print_error:
                messagebox.showinfo("تنبيه", f"تم إنشاء الملف بنجاح في:\n{temp_file_path}\nيرجى فتحه يدوياً للطباعة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة التقرير: {str(e)}")

    def save_report_to_file(self):
        """حفظ التقرير كملف"""
        try:
            from tkinter import filedialog
            from datetime import datetime
            
            # الحصول على محتوى التقرير
            report_content = self.report_text.get(1.0, tk.END)
            
            if not report_content.strip():
                messagebox.showwarning("تحذير", "لا يوجد تقرير لحفظه. يرجى إنشاء تقرير أولاً.")
                return
            
            # اختيار مكان الحفظ
            current_date = datetime.now().strftime("%Y-%m-%d")
            default_filename = f"تقرير_{current_date}.txt"
            
            file_path = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialfile=default_filename,
                title="حفظ التقرير"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(report_content)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ التقرير: {str(e)}")

    def setup_arabic_pdf_support(self):
        """إعداد دعم اللغة العربية في PDF مع خطوط متعددة"""
        try:
            import platform
            registered_fonts = {}

            # قائمة الخطوط العربية المدعومة
            if platform.system() == "Windows":
                font_paths = {
                    'Arabic-Bold': [
                        "C:/Windows/Fonts/arialbd.ttf",
                        "C:/Windows/Fonts/tahomabd.ttf",
                        "C:/Windows/Fonts/calibrib.ttf"
                    ],
                    'Arabic-Regular': [
                        "C:/Windows/Fonts/arial.ttf",
                        "C:/Windows/Fonts/tahoma.ttf",
                        "C:/Windows/Fonts/calibri.ttf",
                        "C:/Windows/Fonts/segoeui.ttf"
                    ],
                    'Arabic-Italic': [
                        "C:/Windows/Fonts/ariali.ttf",
                        "C:/Windows/Fonts/calibrii.ttf"
                    ]
                }
            else:
                # مسارات الخطوط في لينكس/ماك
                font_paths = {
                    'Arabic-Bold': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                        "/System/Library/Fonts/Arial Bold.ttf"
                    ],
                    'Arabic-Regular': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                        "/System/Library/Fonts/Arial.ttf"
                    ],
                    'Arabic-Italic': [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Oblique.ttf"
                    ]
                }

            # تسجيل الخطوط المتوفرة
            for font_name, paths in font_paths.items():
                for font_path in paths:
                    if os.path.exists(font_path):
                        try:
                            pdfmetrics.registerFont(TTFont(font_name, font_path))
                            registered_fonts[font_name] = font_path
                            print(f"✅ تم تسجيل الخط: {font_name}")
                            break
                        except Exception as e:
                            print(f"⚠️ فشل في تسجيل {font_name}: {e}")
                            continue

            # التأكد من وجود خط أساسي على الأقل
            if not registered_fonts:
                print("⚠️ لم يتم العثور على خطوط عربية، سيتم استخدام الخط الافتراضي")
                return False

            self.registered_fonts = registered_fonts
            return True

        except Exception as e:
            print(f"❌ خطأ في إعداد الخطوط العربية: {e}")
            return False

    def create_pdf_styles(self):
        """إنشاء أنماط PDF احترافية أفقية مع دعم كامل للعربية"""
        styles = getSampleStyleSheet()

        # إعداد الخطوط العربية المحسنة
        arabic_support = self.setup_arabic_pdf_support()

        # تحديد الخطوط المناسبة للتخطيط الأفقي
        if arabic_support and hasattr(self, 'registered_fonts') and self.registered_fonts:
            title_font = 'Arabic-Bold' if 'Arabic-Bold' in self.registered_fonts else 'Arabic-Regular'
            heading_font = 'Arabic-Bold' if 'Arabic-Bold' in self.registered_fonts else 'Arabic-Regular'
            normal_font = 'Arabic-Regular' if 'Arabic-Regular' in self.registered_fonts else 'Arabic-Bold'
        else:
            # استخدام خطوط متوافقة مع reportlab
            title_font = heading_font = 'Helvetica-Bold'
            normal_font = 'Helvetica'

        # نمط العنوان الرئيسي - تصميم احترافي
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Title'],
            fontName=title_font,
            fontSize=24,  # خط أكبر للتخطيط الأفقي
            spaceAfter=25,
            spaceBefore=10,
            alignment=TA_CENTER,
            textColor=colors.Color(0.12, 0.23, 0.54),  # أزرق داكن احترافي
            backColor=colors.Color(0.95, 0.95, 0.95),  # خلفية رمادية فاتحة
            borderWidth=2,
            borderColor=colors.Color(0.12, 0.23, 0.54),
            borderPadding=10,
            borderRadius=5
        )

        # نمط العنوان الفرعي - مع خلفية ملونة
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading1'],
            fontName=heading_font,
            fontSize=16,  # خط أكبر للعناوين الفرعية في التخطيط الأفقي
            spaceAfter=12,
            spaceBefore=15,
            alignment=TA_RIGHT,
            textColor=colors.white,
            backColor=colors.Color(0.2, 0.6, 0.2),  # أخضر احترافي
            borderPadding=8,
            leftIndent=10,
            rightIndent=10
        )

        # نمط النص العادي - محسن للقراءة
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontName=normal_font,
            fontSize=11,
            spaceAfter=8,
            spaceBefore=2,
            alignment=TA_RIGHT,
            rightIndent=25,
            leftIndent=25,
            textColor=colors.Color(0.2, 0.2, 0.2),  # رمادي داكن للنص
            leading=16  # تباعد الأسطر
        )

        # نمط النقاط - مميز
        bullet_style = ParagraphStyle(
            'CustomBullet',
            parent=normal_style,
            leftIndent=35,
            bulletIndent=15,
            bulletFontName=heading_font,
            bulletColor=colors.Color(0.8, 0.4, 0.0),  # برتقالي للنقاط
            spaceAfter=4
        )

        # نمط التاريخ والوقت
        date_style = ParagraphStyle(
            'CustomDate',
            parent=styles['Normal'],
            fontName=normal_font,
            fontSize=10,
            spaceAfter=15,
            alignment=TA_CENTER,
            textColor=colors.Color(0.5, 0.5, 0.5),  # رمادي متوسط
            backColor=colors.Color(0.98, 0.98, 0.98)  # خلفية رمادية فاتحة جداً
        )

        # نمط الإحصائيات - مميز بألوان
        stats_style = ParagraphStyle(
            'CustomStats',
            parent=normal_style,
            backColor=colors.Color(0.9, 0.95, 1.0),  # خلفية زرقاء فاتحة
            borderWidth=1,
            borderColor=colors.Color(0.7, 0.8, 0.9),
            borderPadding=6,
            spaceAfter=10
        )

        return {
            'title': title_style,
            'heading': heading_style,
            'normal': normal_style,
            'bullet': bullet_style,
            'date': date_style,
            'stats': stats_style
        }

    def export_current_report_to_pdf(self):
        """تصدير التقرير الحالي إلى PDF"""
        if not PDF_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة. يرجى تثبيتها أولاً.")
            return

        try:
            from tkinter import filedialog

            # الحصول على محتوى التقرير
            report_content = self.report_text.get(1.0, tk.END)

            if not report_content.strip():
                messagebox.showwarning("تحذير", "لا يوجد تقرير لتصديره. يرجى إنشاء تقرير أولاً.")
                return

            # اختيار مكان الحفظ
            current_date = datetime.now().strftime("%Y-%m-%d")
            default_filename = f"تقرير_{current_date}.pdf"

            file_path = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                initialfile=default_filename,
                title="حفظ التقرير كـ PDF"
            )

            if file_path:
                self.create_pdf_report(report_content, file_path)
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى PDF بنجاح:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير إلى PDF: {str(e)}")

    def export_all_reports_to_pdf(self):
        """تصدير جميع التقارير إلى PDF"""
        if not PDF_AVAILABLE:
            messagebox.showerror("خطأ", "مكتبة reportlab غير متوفرة. يرجى تثبيتها أولاً.")
            return

        try:
            from tkinter import filedialog

            # اختيار مجلد الحفظ
            folder_path = filedialog.askdirectory(title="اختر مجلد لحفظ التقارير")

            if not folder_path:
                return

            # إنشاء جميع التقارير
            reports = {
                "تقرير_المشاريع": self.get_projects_report_content(),
                "تقرير_الصيانة": self.get_maintenance_report_content(),
                "تقرير_المباني": self.get_buildings_report_content(),
                "التقرير_المالي": self.get_financial_report_content(),
                "تقرير_الأداء": self.get_performance_report_content(),
                "التقرير_السنوي": self.get_annual_report_content()
            }

            current_date = datetime.now().strftime("%Y-%m-%d")
            success_count = 0

            for report_name, report_content in reports.items():
                try:
                    file_path = os.path.join(folder_path, f"{report_name}_{current_date}.pdf")
                    self.create_pdf_report(report_content, file_path, report_name)
                    success_count += 1
                except Exception as e:
                    print(f"فشل في إنشاء {report_name}: {e}")

            messagebox.showinfo("نجح", f"تم تصدير {success_count} تقرير إلى PDF بنجاح في:\n{folder_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقارير: {str(e)}")

    def create_pdf_report(self, content, file_path, title="تقرير"):
        """إنشاء ملف PDF احترافي أفقي مع دعم كامل للعربية وتصميم محسن"""
        try:
            # إنشاء مستند PDF بتخطيط أفقي محسن للطباعة
            from reportlab.lib.pagesizes import landscape, A4

            doc = SimpleDocTemplate(
                file_path,
                pagesize=landscape(A4),  # تخطيط أفقي
                rightMargin=1.5*cm,
                leftMargin=1.5*cm,
                topMargin=1.5*cm,
                bottomMargin=1.5*cm,
                title=title,
                author="نظام إدارة أعمال الإدارة الهندسية",
                subject="تقرير إداري أفقي",
                creator="Engineering Management System - Landscape Mode"
            )

            # إنشاء الأنماط المحسنة
            styles = self.create_pdf_styles()

            # قائمة العناصر
            story = []

            # إضافة رأس الصفحة مع شعار وهوية
            self.add_pdf_header(story, styles, title)

            # إضافة تاريخ ووقت التقرير
            current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
            story.append(Paragraph(f"📅 تاريخ إنشاء التقرير: {current_time}", styles['date']))
            story.append(Spacer(1, 15))

            # معالجة المحتوى بطريقة محسنة
            self.process_content_for_pdf(content, story, styles)

            # إضافة تذييل الصفحة
            self.add_pdf_footer(story, styles)

            # بناء المستند مع معالجة الأخطاء
            doc.build(story, onFirstPage=self.add_page_number, onLaterPages=self.add_page_number)

            # فتح نافذة الطباعة تلقائياً (اختياري)
            self.auto_print_pdf(file_path)

        except Exception as e:
            raise Exception(f"فشل في إنشاء PDF: {str(e)}")

    def add_pdf_header(self, story, styles, title):
        """إضافة رأس احترافي للصفحة"""
        # خط فاصل علوي
        story.append(Spacer(1, 10))

        # العنوان الرئيسي مع تصميم مميز
        story.append(Paragraph(f"🏗️ {title} 🏗️", styles['title']))
        story.append(Spacer(1, 10))

        # معلومات النظام
        system_info = "نظام إدارة أعمال الإدارة الهندسية - الإصدار المحسن 2.0"
        story.append(Paragraph(system_info, styles['date']))
        story.append(Spacer(1, 20))

    def process_content_for_pdf(self, content, story, styles):
        """معالجة المحتوى بطريقة احترافية مع تمييز الألوان"""
        lines = content.split('\n')
        current_section = None

        for line in lines:
            line = line.strip()
            if not line:
                story.append(Spacer(1, 4))
                continue

            # تحديد نوع السطر وتطبيق التنسيق المناسب
            if line.startswith('=') or line.startswith('-'):
                # خط فاصل - إضافة مساحة
                story.append(Spacer(1, 8))
                continue

            elif any(line.startswith(prefix) for prefix in ['📊', '🔧', '🏢', '💰', '📈', '📅']):
                # عنوان رئيسي مع خلفية ملونة
                story.append(Spacer(1, 10))
                story.append(Paragraph(line, styles['heading']))
                current_section = line

            elif line.startswith('•'):
                # نقطة مع تمييز لوني
                if any(keyword in line for keyword in ['متوفر', 'مكتمل', 'نجح', 'ممتاز']):
                    # أخضر للحالات الإيجابية
                    bullet_style = self.create_colored_style(styles['bullet'], colors.darkgreen)
                elif any(keyword in line for keyword in ['غير متوفر', 'فشل', 'متأخر', 'مؤجل']):
                    # أحمر للحالات السلبية
                    bullet_style = self.create_colored_style(styles['bullet'], colors.darkred)
                else:
                    # اللون الافتراضي
                    bullet_style = styles['bullet']

                story.append(Paragraph(line, bullet_style))

            elif ':' in line and any(keyword in line for keyword in ['إجمالي', 'متوسط', 'نسبة']):
                # إحصائيات مميزة
                story.append(Paragraph(line, styles['stats']))

            else:
                # نص عادي
                story.append(Paragraph(line, styles['normal']))

    def create_colored_style(self, base_style, color):
        """إنشاء نمط ملون مخصص"""
        from reportlab.lib.styles import ParagraphStyle
        return ParagraphStyle(
            'ColoredStyle',
            parent=base_style,
            textColor=color,
            fontName=base_style.fontName,
            fontSize=base_style.fontSize
        )

    def add_pdf_footer(self, story, styles):
        """إضافة تذييل احترافي للصفحة"""
        story.append(Spacer(1, 30))

        # خط فاصل
        story.append(Spacer(1, 10))

        # معلومات التذييل
        footer_text = "تم إنشاء هذا التقرير بواسطة نظام إدارة أعمال الإدارة الهندسية"
        story.append(Paragraph(footer_text, styles['date']))

        # معلومات الاتصال
        contact_info = "للاستفسارات والدعم الفني - قسم تقنية المعلومات"
        story.append(Paragraph(contact_info, styles['date']))

    def add_page_number(self, canvas, doc):
        """إضافة رقم الصفحة للتخطيط الأفقي"""
        from reportlab.lib.pagesizes import landscape, A4
        canvas.saveState()
        canvas.setFont('Helvetica', 10)  # خط متوافق مع reportlab
        page_num = canvas.getPageNumber()
        text = f"صفحة {page_num}"
        # استخدام أبعاد التخطيط الأفقي
        page_width = landscape(A4)[0]
        canvas.drawRightString(page_width - 2*cm, 1*cm, text)
        canvas.restoreState()

    def auto_print_pdf(self, file_path):
        """فتح نافذة الطباعة تلقائياً بطريقة آمنة"""
        try:
            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                # استخدام طريقة آمنة لفتح الملف
                try:
                    # محاولة فتح الملف مع برنامج PDF الافتراضي
                    subprocess.run(['start', '', file_path], shell=True, check=False)
                except:
                    # في حالة فشل الطريقة الأولى، استخدم os.startfile
                    try:
                        import os
                        os.startfile(file_path)
                    except:
                        print(f"تحذير: لا يمكن فتح الملف تلقائياً: {file_path}")
            elif system == "Darwin":  # macOS
                subprocess.run(['open', file_path], check=False)
            elif system == "Linux":
                subprocess.run(['xdg-open', file_path], check=False)

        except Exception as e:
            print(f"تحذير: لا يمكن فتح الملف تلقائياً: {e}")
            # لا نرفع خطأ هنا لأن إنشاء PDF نجح

    def create_professional_table(self, data, headers, title="جدول"):
        """إنشاء جدول احترافي أفقي مع صفوف متناوبة الألوان ودعم كامل للعربية"""
        try:
            # إنشاء بيانات الجدول
            table_data = [headers] + data

            # إنشاء الجدول مع عرض مناسب للتخطيط الأفقي
            from reportlab.lib.pagesizes import landscape, A4
            page_width = landscape(A4)[0] - 3*cm  # عرض الصفحة الأفقية مطروحاً منه الهوامش
            col_width = page_width / len(headers)  # توزيع العرض بالتساوي

            table = Table(table_data, repeatRows=1, colWidths=[col_width] * len(headers))

            # تنسيق الجدول الاحترافي
            table_style = TableStyle([
                # تنسيق الرأس - محسن للتخطيط الأفقي
                ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.12, 0.23, 0.54)),  # أزرق داكن احترافي
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),  # خط متوافق
                ('FONTSIZE', (0, 0), (-1, 0), 14),  # خط أكبر للتخطيط الأفقي
                ('BOTTOMPADDING', (0, 0), (-1, 0), 15),

                # تنسيق البيانات - محسن للتخطيط الأفقي
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),  # خط متوافق
                ('FONTSIZE', (0, 1), (-1, -1), 12),  # خط أكبر للقراءة الأفضل
                ('GRID', (0, 0), (-1, -1), 1, colors.black),

                # صفوف متناوبة الألوان
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.Color(0.95, 0.95, 0.95)]),

                # حدود وتباعد
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('LEFTPADDING', (0, 0), (-1, -1), 6),
                ('RIGHTPADDING', (0, 0), (-1, -1), 6),
                ('TOPPADDING', (0, 0), (-1, -1), 6),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ])

            # تطبيق تمييز لوني للحالات
            for row_idx, row in enumerate(data, 1):
                for col_idx, cell in enumerate(row):
                    if isinstance(cell, str):
                        if any(keyword in cell for keyword in ['متوفر', 'مكتمل', 'نشط']):
                            # أخضر للحالات الإيجابية
                            table_style.add('TEXTCOLOR', (col_idx, row_idx), (col_idx, row_idx), colors.darkgreen)
                            table_style.add('BACKGROUND', (col_idx, row_idx), (col_idx, row_idx), colors.Color(0.9, 1.0, 0.9))
                        elif any(keyword in cell for keyword in ['غير متوفر', 'معطل', 'مؤجل']):
                            # أحمر للحالات السلبية
                            table_style.add('TEXTCOLOR', (col_idx, row_idx), (col_idx, row_idx), colors.darkred)
                            table_style.add('BACKGROUND', (col_idx, row_idx), (col_idx, row_idx), colors.Color(1.0, 0.9, 0.9))

            table.setStyle(table_style)
            return table

        except Exception as e:
            print(f"خطأ في إنشاء الجدول: {e}")
            return None

    def add_statistics_section(self, story, styles, stats_data):
        """إضافة قسم الإحصائيات بتصميم احترافي"""
        # عنوان القسم
        story.append(Paragraph("📊 الإحصائيات الرئيسية", styles['heading']))
        story.append(Spacer(1, 10))

        # إنشاء جدول الإحصائيات
        headers = ['المؤشر', 'القيمة', 'النسبة', 'الحالة']

        # بيانات وهمية للمثال
        sample_stats = [
            ['المشاريع المكتملة', '8', '32%', 'مكتمل'],
            ['المشاريع النشطة', '12', '48%', 'نشط'],
            ['المشاريع المؤجلة', '3', '12%', 'مؤجل'],
            ['البلاغات المفتوحة', '25', '62%', 'متوفر'],
            ['معدل الإنجاز', '68%', '68%', 'مكتمل']
        ]

        stats_table = self.create_professional_table(sample_stats, headers, "إحصائيات")
        if stats_table:
            story.append(stats_table)
            story.append(Spacer(1, 20))

    def get_projects_report_content(self):
        """الحصول على محتوى تقرير المشاريع المحسن"""
        return f"""
📊 تقرير شامل للمشاريع الهندسية

📅 تاريخ التقرير: {datetime.now().strftime("%Y/%m/%d %H:%M")}

📈 ملخص المشاريع الإجمالي:
• إجمالي المشاريع المسجلة: 25 مشروع
• المشاريع المكتملة بنجاح: 8 مشاريع (32%)
• المشاريع قيد التنفيذ النشط: 12 مشروع (48%)
• المشاريع المؤجلة مؤقتاً: 3 مشاريع (12%)
• المشاريع الملغية نهائياً: 2 مشروع (8%)

🏗️ توزيع المشاريع حسب النوع:
• المشاريع السكنية: 15 مشروع (60%)
• المشاريع التجارية: 8 مشاريع (32%)
• المشاريع الإدارية: 12 مشروع (48%)
• المشاريع الصناعية: 5 مشاريع (20%)

💰 التحليل المالي الشامل:
• إجمالي الميزانية المعتمدة: 50,000,000 ريال سعودي
• المبلغ المنفق حتى تاريخه: 32,000,000 ريال (64%)
• المبلغ المتبقي للإنفاق: 18,000,000 ريال (36%)
• متوسط تكلفة المشروع الواحد: 2,000,000 ريال
• نسبة التوفير المحققة: 8.5%

📊 مؤشرات الأداء الرئيسية:
• متوسط نسبة الإنجاز العامة: 68%
• المشاريع الملتزمة بالجدول الزمني: 18 مشروع (72%)
• المشاريع المتأخرة عن الموعد: 4 مشاريع (16%)
• المشاريع المنجزة قبل الموعد: 3 مشاريع (12%)
• معدل رضا العملاء: 85%

⚡ حالة المشاريع النشطة:
• مشاريع بحالة ممتازة: 15 مشروع
• مشاريع تحتاج متابعة: 7 مشاريع
• مشاريع تواجه تحديات: 3 مشاريع

🎯 التوصيات الاستراتيجية:
• تكثيف متابعة المشاريع المتأخرة وإعادة جدولتها
• تحسين عمليات التخطيط الزمني والتنبؤ بالمخاطر
• زيادة الرقابة على جودة التنفيذ ومعايير السلامة
• تطوير نظام إنذار مبكر للمشاكل المحتملة
• تعزيز التواصل مع المقاولين والموردين
        """

    def get_maintenance_report_content(self):
        """الحصول على محتوى تقرير الصيانة المحسن"""
        return f"""
🔧 تقرير الصيانة الشهري المفصل

📅 تاريخ التقرير: {datetime.now().strftime("%Y/%m/%d %H:%M")}

📈 إحصائيات بلاغات الصيانة الشاملة:
• إجمالي البلاغات المسجلة: 40 بلاغ
• البلاغات المكتملة بنجاح: 15 بلاغ (37.5%)
• البلاغات قيد التنفيذ النشط: 8 بلاغات (20%)
• البلاغات الجديدة غير المعالجة: 12 بلاغ (30%)
• البلاغات المؤجلة لأسباب فنية: 5 بلاغات (12.5%)

🔧 تصنيف الأعطال حسب النوع:
• أعطال الأنظمة الكهربائية: 12 بلاغ (30%)
• أعطال شبكات السباكة والصرف: 8 بلاغات (20%)
• أعطال أنظمة التكييف والتهوية: 15 بلاغ (37.5%)
• أعطال متنوعة وصيانة عامة: 5 بلاغات (12.5%)

⚡ توزيع البلاغات حسب مستوى الأولوية:
• أولوية عالية (طوارئ): 10 بلاغات (25%)
• أولوية متوسطة (مهمة): 20 بلاغ (50%)
• أولوية منخفضة (روتينية): 10 بلاغات (25%)

🏢 توزيع البلاغات حسب المواقع:
• المبنى الإداري الرئيسي: 15 بلاغ (37.5%)
• مبنى كلية الهندسة: 12 بلاغ (30%)
• مبنى المختبرات والورش: 8 بلاغات (20%)
• المباني الخدمية الأخرى: 5 بلاغات (12.5%)

⏱️ مؤشرات الأداء الزمني:
• متوسط وقت الاستجابة للأولوية العالية: 2 ساعة
• متوسط وقت الاستجابة للأولوية المتوسطة: 8 ساعات
• متوسط وقت الاستجابة للأولوية المنخفضة: 24 ساعة
• متوسط وقت إنجاز الأعمال: 3.5 أيام
• نسبة الالتزام بالمواعيد المحددة: 78%

💰 التكاليف والميزانية:
• إجمالي تكاليف الصيانة الشهرية: 45,000 ريال
• تكلفة قطع الغيار والمواد: 18,000 ريال (40%)
• تكلفة العمالة والخدمات: 27,000 ريال (60%)
• متوسط تكلفة البلاغ الواحد: 1,125 ريال

🎯 التوصيات الاستراتيجية للتحسين:
• تقليل أوقات الاستجابة للبلاغات العاجلة
• زيادة عدد فريق الصيانة المتخصص
• تطبيق برنامج الصيانة الوقائية المنتظمة
• تحديث مخزون قطع الغيار الأساسية
• تدريب الفنيين على التقنيات الحديثة
• تطوير نظام متابعة إلكتروني محسن
        """

    def get_buildings_report_content(self):
        """الحصول على محتوى تقرير المباني"""
        return """
🏢 تقرير حالة المباني

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🏗️ ملخص المباني:
• إجمالي المباني: 8 مباني
• المباني النشطة: 6 مباني (75%)
• المباني قيد الصيانة: 1 مبنى (12.5%)
• المباني المغلقة مؤقتاً: 1 مبنى (12.5%)

🏢 المباني حسب النوع:
• مباني إدارية: 3 مباني
• مباني أكاديمية: 2 مبنى
• مباني خدمية: 2 مبنى
• مباني تقنية: 1 مبنى

📐 المساحات:
• إجمالي المساحة: 12,500 م²
• متوسط المساحة: 1,562 م²
• أكبر مبنى: 2,500 م²
• أصغر مبنى: 800 م²

📅 أعمار المباني:
• مباني حديثة (أقل من 5 سنوات): 4 مباني
• مباني متوسطة (5-10 سنوات): 3 مباني
• مباني قديمة (أكثر من 10 سنوات): 1 مبنى

🔧 حالة الصيانة:
• مباني بحالة ممتازة: 5 مباني
• مباني بحالة جيدة: 2 مبنى
• مباني تحتاج صيانة: 1 مبنى

💡 استهلاك الطاقة:
• متوسط الاستهلاك الشهري: 45,000 كيلو واط
• تكلفة الكهرباء الشهرية: 18,000 ريال
• توفير الطاقة المحقق: 15%

🎯 التوصيات:
• تحديث أنظمة التكييف
• تحسين العزل الحراري
• استخدام الطاقة المتجددة
        """

    def get_financial_report_content(self):
        """الحصول على محتوى التقرير المالي"""
        return """
💰 التقرير المالي

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

💵 الميزانية العامة:
• إجمالي الميزانية: 75,000,000 ريال
• المبلغ المنفق: 48,500,000 ريال (64.7%)
• المبلغ المتبقي: 26,500,000 ريال (35.3%)

🏗️ مصروفات المشاريع:
• مشاريع البناء: 32,000,000 ريال
• مشاريع التطوير: 8,500,000 ريال
• مشاريع الصيانة: 3,000,000 ريال
• مشاريع أخرى: 5,000,000 ريال

🔧 مصروفات الصيانة:
• الصيانة الطارئة: 1,200,000 ريال
• الصيانة الوقائية: 800,000 ريال
• قطع الغيار: 600,000 ريال
• العمالة: 400,000 ريال

🏢 مصروفات التشغيل:
• الكهرباء: 216,000 ريال/سنوياً
• المياه: 84,000 ريال/سنوياً
• التنظيف: 120,000 ريال/سنوياً
• الأمن: 180,000 ريال/سنوياً

📊 التحليل المالي:
• معدل الإنفاق الشهري: 4,041,667 ريال
• توقع الإنفاق السنوي: 48,500,000 ريال
• نسبة التوفير: 8.5%
• العائد على الاستثمار: 12%

🎯 التوصيات المالية:
• تحسين إدارة التكاليف
• زيادة كفاءة الإنفاق
• البحث عن مصادر تمويل إضافية
        """

    def get_performance_report_content(self):
        """الحصول على محتوى تقرير الأداء"""
        return """
📈 تقرير الأداء

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🎯 مؤشرات الأداء الرئيسية:
• نسبة إنجاز المشاريع: 68%
• نسبة المشاريع في الموعد: 72%
• رضا العملاء: 85%
• كفاءة استخدام الموارد: 78%

⏱️ الأداء الزمني:
• متوسط وقت إنجاز المشاريع: 8.5 شهر
• متوسط التأخير: 15 يوم
• نسبة المشاريع المبكرة: 12%
• نسبة المشاريع المتأخرة: 16%

💰 الأداء المالي:
• نسبة الالتزام بالميزانية: 92%
• متوسط توفير التكاليف: 8.5%
• العائد على الاستثمار: 12%
• كفاءة الإنفاق: 88%

🔧 أداء الصيانة:
• وقت الاستجابة للأعطال: 4 ساعات
• نسبة حل المشاكل من المرة الأولى: 78%
• رضا المستخدمين عن الصيانة: 82%
• تكلفة الصيانة لكل متر مربع: 24 ريال

👥 أداء الفريق:
• إنتاجية الفريق: 85%
• نسبة الحضور: 96%
• معدل دوران الموظفين: 5%
• رضا الموظفين: 88%

🏆 الإنجازات:
• إنجاز 8 مشاريع بنجاح
• توفير 4.2 مليون ريال
• تحسين كفاءة الطاقة بنسبة 15%
• حصول على شهادة الجودة ISO

🎯 مجالات التحسين:
• تقليل وقت إنجاز المشاريع
• تحسين التخطيط المسبق
• زيادة الاستثمار في التدريب
        """

    def get_annual_report_content(self):
        """الحصول على محتوى التقرير السنوي"""
        return """
📅 التقرير السنوي 2024

📅 تاريخ التقرير: """ + datetime.now().strftime("%Y/%m/%d %H:%M") + """

🎯 ملخص تنفيذي:
تم خلال عام 2024 إنجاز العديد من المشاريع الهامة وتحقيق نتائج إيجابية
في جميع المجالات. شهد العام نمواً ملحوظاً في الأداء والكفاءة.

📊 الإنجازات الرئيسية:
• إنجاز 25 مشروع بقيمة 50 مليون ريال
• تحسين كفاءة الطاقة بنسبة 15%
• تقليل تكاليف الصيانة بنسبة 12%
• زيادة رضا العملاء إلى 85%

🏗️ المشاريع المنجزة:
• مشروع المبنى الإداري الجديد
• تطوير نظام إدارة المرافق
• تحديث أنظمة التكييف
• مشروع الطاقة الشمسية

💰 الأداء المالي:
• إجمالي الإيرادات: 65 مليون ريال
• إجمالي المصروفات: 48.5 مليون ريال
• صافي الربح: 16.5 مليون ريال
• نسبة الربحية: 25.4%

🔧 تطوير الصيانة:
• تطبيق نظام الصيانة الذكية
• تدريب فرق الصيانة
• تحديث المعدات والأدوات
• تحسين أوقات الاستجابة

🏢 تطوير المرافق:
• إضافة 3 مباني جديدة
• تحديث 5 مباني قائمة
• تحسين البنية التحتية
• تطوير المساحات الخضراء

👥 تطوير الموارد البشرية:
• توظيف 15 موظف جديد
• تدريب 50 موظف
• تحسين بيئة العمل
• زيادة الرواتب بنسبة 8%

🌱 الاستدامة والبيئة:
• تقليل استهلاك الطاقة بنسبة 15%
• تقليل استهلاك المياه بنسبة 10%
• إعادة تدوير 80% من النفايات
• زراعة 200 شجرة

🎯 خطط العام القادم:
• إنجاز 30 مشروع جديد
• زيادة الاستثمار في التقنية
• تطوير الموارد البشرية
• تحسين الاستدامة البيئية

📈 التوقعات:
• نمو الإيرادات بنسبة 20%
• تحسين الكفاءة بنسبة 15%
• زيادة رضا العملاء إلى 90%
• تقليل التكاليف بنسبة 10%
        """