#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وجود الوظائف في الملفات
"""

import sys
import os

# إضافة المسار الحالي لـ Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_functions():
    """اختبار وجود الوظائف"""
    try:
        print("🔍 اختبار وجود وظائف الطباعة...")
        
        # اختبار ملف المشاريع
        print("\n📁 اختبار ملف المشاريع:")
        from simple_project_management import SimpleProjectManagementWindow
        
        # التحقق من وجود الوظائف
        functions_to_check = [
            'print_projects_report',
            'get_projects_data_for_report', 
            'create_projects_pdf_report',
            'load_sample_projects'
        ]
        
        for func_name in functions_to_check:
            if hasattr(SimpleProjectManagementWindow, func_name):
                print(f"✅ {func_name} موجودة")
            else:
                print(f"❌ {func_name} غير موجودة")
        
        # اختبار ملف المباني
        print("\n🏢 اختبار ملف المباني:")
        from buildings_management import BuildingsManagementWindow
        
        functions_to_check = [
            'print_buildings_report',
            'get_buildings_data_for_report',
            'create_buildings_pdf_report',
            'load_sample_buildings'
        ]
        
        for func_name in functions_to_check:
            if hasattr(BuildingsManagementWindow, func_name):
                print(f"✅ {func_name} موجودة")
            else:
                print(f"❌ {func_name} غير موجودة")
        
        # اختبار ملف الصيانة
        print("\n🔧 اختبار ملف الصيانة:")
        from maintenance_management import MaintenanceManagementWindow
        
        functions_to_check = [
            'print_maintenance_report',
            'get_maintenance_data_for_report',
            'create_maintenance_pdf_report',
            'load_sample_maintenance'
        ]
        
        for func_name in functions_to_check:
            if hasattr(MaintenanceManagementWindow, func_name):
                print(f"✅ {func_name} موجودة")
            else:
                print(f"❌ {func_name} غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_functions()
