#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط للتخطيط الأفقي
Simple Landscape Layout Test
"""

import os
import tempfile
from datetime import datetime

def test_simple_landscape():
    """اختبار مبسط للتخطيط الأفقي"""
    print("🔍 اختبار مبسط للتخطيط الأفقي...")
    
    try:
        # التحقق من وجود مكتبة reportlab
        from reportlab.lib.pagesizes import A4, landscape
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Table, TableStyle
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib import colors
        from reportlab.lib.enums import TA_CENTER
        from reportlab.lib.units import cm
        
        print("✅ مكتبة reportlab متوفرة")
        
        # إنشاء ملف PDF تجريبي أفقي
        with tempfile.NamedTemporaryFile(suffix='_simple_landscape.pdf', delete=False) as temp_file:
            pdf_path = temp_file.name
        
        # إنشاء مستند PDF أفقي
        doc = SimpleDocTemplate(
            pdf_path, 
            pagesize=landscape(A4),  # تخطيط أفقي
            rightMargin=1.5*cm,
            leftMargin=1.5*cm,
            topMargin=1.5*cm,
            bottomMargin=1.5*cm
        )
        
        styles = getSampleStyleSheet()
        story = []
        
        # عنوان بسيط
        story.append(Paragraph("تقرير اختبار التخطيط الأفقي", styles['Title']))
        story.append(Paragraph("تم إنشاؤه في: " + datetime.now().strftime("%Y-%m-%d %H:%M"), styles['Normal']))
        
        # جدول بسيط
        table_data = [
            ['العنصر', 'النوع', 'الحالة', 'التقييم'],
            ['مشروع 1', 'انشائي', 'مكتمل', 'ممتاز'],
            ['مشروع 2', 'اداري', 'قيد التنفيذ', 'جيد'],
            ['مشروع 3', 'صيانة', 'مجدولة', 'مخطط']
        ]
        
        # حساب عرض الأعمدة للتخطيط الأفقي
        page_width = landscape(A4)[0] - 3*cm
        col_widths = [page_width*0.3, page_width*0.2, page_width*0.25, page_width*0.25]
        
        table = Table(table_data, repeatRows=1, colWidths=col_widths)
        table.setStyle(TableStyle([
            # تنسيق الرأس
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 15),
            
            # تنسيق البيانات
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 12),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 8),
            ('RIGHTPADDING', (0, 0), (-1, -1), 8),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ]))
        
        story.append(table)
        
        # بناء المستند
        doc.build(story)
        
        if os.path.exists(pdf_path):
            size = os.path.getsize(pdf_path)
            print(f"✅ تم إنشاء PDF أفقي بنجاح: {size:,} بايت")
            print(f"📄 مسار الملف: {pdf_path}")
            
            # فتح الملف للمعاينة
            try:
                import subprocess
                import platform
                system = platform.system()
                if system == "Windows":
                    subprocess.run(['start', '', pdf_path], shell=True, check=False)
                    print("✅ تم فتح الملف للمعاينة")
            except:
                print("⚠️ لم يتم فتح الملف تلقائياً")
            
            return True, pdf_path
        else:
            print("❌ فشل في إنشاء PDF")
            return False, None
            
    except ImportError:
        print("❌ مكتبة reportlab غير متوفرة")
        return False, None
    except Exception as e:
        print(f"❌ خطأ في إنشاء PDF: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_reports_module():
    """اختبار وحدة التقارير"""
    print("🔍 اختبار وحدة التقارير...")
    
    try:
        # اختبار إنشاء PDF من وحدة التقارير
        from reports_management import ReportsManagementWindow
        import tkinter as tk
        
        # إنشاء نافذة وهمية
        root = tk.Tk()
        root.withdraw()
        
        # إنشاء نافذة التقارير
        reports_window = ReportsManagementWindow(root, None, None)
        
        # اختبار إنشاء PDF
        test_content = """
🏗️ تقرير اختبار نظام إدارة أعمال الإدارة الهندسية

📋 الإحصائيات:
• إجمالي المشاريع: 15
• المشاريع المكتملة: 8
• المشاريع النشطة: 7

📊 التفاصيل:
✅ مشروع المبنى الإداري - مكتمل
🔄 مشروع تطوير المختبرات - قيد التنفيذ
📅 مشروع الصيانة الدورية - مجدولة

✅ الاختبار مكتمل بنجاح!
        """
        
        with tempfile.NamedTemporaryFile(suffix='_reports_test.pdf', delete=False) as temp_file:
            reports_window.create_pdf_report(test_content, temp_file.name, "تقرير اختبار")
            
            if os.path.exists(temp_file.name):
                size = os.path.getsize(temp_file.name)
                print(f"✅ تم إنشاء PDF من وحدة التقارير: {size:,} بايت")
                print(f"📄 مسار الملف: {temp_file.name}")
                
                # فتح الملف للمعاينة
                try:
                    import subprocess
                    import platform
                    system = platform.system()
                    if system == "Windows":
                        subprocess.run(['start', '', temp_file.name], shell=True, check=False)
                        print("✅ تم فتح الملف للمعاينة")
                except:
                    print("⚠️ لم يتم فتح الملف تلقائياً")
                
                root.destroy()
                return True, temp_file.name
            else:
                print("❌ فشل في إنشاء PDF من وحدة التقارير")
                root.destroy()
                return False, None
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدة التقارير: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🧪 اختبار مبسط للتقارير الأفقية")
    print("=" * 70)
    print("📋 الهدف: التأكد من عمل التخطيط الأفقي")
    print("=" * 70)
    
    # تشغيل الاختبارات
    tests = [
        ("PDF أفقي مبسط", test_simple_landscape),
        ("وحدة التقارير", test_reports_module)
    ]
    
    results = []
    pdf_files = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        result, path = test_func()
        if path:
            pdf_files.append(path)
        results.append((test_name, result))
    
    # عرض النتائج
    print("\n" + "=" * 70)
    print("📋 نتائج الاختبارات:")
    print("=" * 70)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        if result:
            print(f"✅ {test_name}: نجح")
            passed += 1
        else:
            print(f"❌ {test_name}: فشل")
    
    print(f"\n📊 الإحصائيات: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("✅ التخطيط الأفقي يعمل بشكل صحيح")
        print("✅ وحدة التقارير محسنة")
    elif passed >= 1:
        print("\n✅ معظم الاختبارات نجحت!")
        print("✅ الميزات الأساسية تعمل")
    else:
        print("\n⚠️ جميع الاختبارات فشلت")
        print("يرجى مراجعة الأخطاء أعلاه")
    
    if pdf_files:
        print(f"\n📄 ملفات PDF المنشأة:")
        for i, path in enumerate(pdf_files, 1):
            print(f"   {i}. {path}")
        print("يمكنك فتحها لمعاينة التحسينات")
    
    print("\n📝 الميزات المطبقة:")
    print("• تخطيط أفقي (Landscape) - أفضل لعرض الجداول")
    print("• خطوط أكبر وأوضح - مناسبة للطباعة")
    print("• جداول محسنة - عرض أعمدة متناسب")
    print("• ألوان احترافية - تصميم منسق")
    print("=" * 70)

if __name__ == "__main__":
    main()
