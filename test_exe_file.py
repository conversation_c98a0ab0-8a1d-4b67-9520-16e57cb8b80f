#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الملف التنفيذي المُنشأ
Test the created executable file
"""

import os
import subprocess
import time
from pathlib import Path

def test_exe_file():
    """اختبار الملف التنفيذي"""
    print("🧪 اختبار الملف التنفيذي...")
    
    # مسار الملف التنفيذي
    exe_path = Path('dist/نظام_إدارة_أعمال_الإدارة_الهندسية/نظام_إدارة_أعمال_الإدارة_الهندسية.exe')
    
    if not exe_path.exists():
        print("❌ الملف التنفيذي غير موجود")
        return False
    
    # التحقق من حجم الملف
    file_size = exe_path.stat().st_size / (1024 * 1024)  # بالميجابايت
    print(f"📁 حجم الملف التنفيذي: {file_size:.1f} MB")
    
    if file_size < 10:
        print("⚠️ حجم الملف صغير، قد تكون هناك مشكلة")
        return False
    
    print("✅ الملف التنفيذي موجود وحجمه مناسب")
    
    # اختبار تشغيل سريع (بدون انتظار)
    try:
        print("🚀 اختبار تشغيل الملف التنفيذي...")
        
        # تشغيل الملف مع timeout قصير للتحقق من عدم وجود أخطاء فورية
        process = subprocess.Popen(
            [str(exe_path)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=exe_path.parent
        )
        
        # انتظار قصير للتحقق من عدم وجود أخطاء فورية
        time.sleep(3)
        
        # التحقق من حالة العملية
        poll = process.poll()
        if poll is None:
            print("✅ الملف التنفيذي يعمل بشكل طبيعي")
            
            # إنهاء العملية
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return True
        else:
            # العملية انتهت بسرعة، قد تكون هناك مشكلة
            stdout, stderr = process.communicate()
            print(f"⚠️ الملف التنفيذي انتهى بسرعة (كود الخروج: {poll})")
            if stderr:
                print(f"خطأ: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الملف التنفيذي: {e}")
        return False

def check_distribution_files():
    """التحقق من ملفات التوزيع"""
    print("\n📋 التحقق من ملفات التوزيع...")
    
    # الملفات المطلوبة
    required_files = [
        'dist/نظام_إدارة_أعمال_الإدارة_الهندسية/نظام_إدارة_أعمال_الإدارة_الهندسية.exe',
        'dist/نظام_إدارة_أعمال_الإدارة_الهندسية/engineering_system.db',
        'dist/نظام_إدارة_أعمال_الإدارة_الهندسية/تشغيل_النظام.bat',
        'dist/نظام_إدارة_أعمال_الإدارة_الهندسية/اقرأني.txt',
        'dist/نظام_إدارة_أعمال_الإدارة_الهندسية/_internal'
    ]
    
    all_present = True
    for file_path in required_files:
        path = Path(file_path)
        if path.exists():
            if path.is_file():
                size = path.stat().st_size / 1024  # بالكيلوبايت
                print(f"   ✅ {path.name} ({size:.1f} KB)")
            else:
                print(f"   ✅ {path.name}/ (مجلد)")
        else:
            print(f"   ❌ {path.name} - غير موجود")
            all_present = False
    
    return all_present

def check_zip_file():
    """التحقق من الملف المضغوط"""
    print("\n🗜️ التحقق من الملف المضغوط...")
    
    zip_files = list(Path('dist').glob('*.zip'))
    if not zip_files:
        print("❌ لم يتم العثور على ملف مضغوط")
        return False
    
    for zip_file in zip_files:
        size = zip_file.stat().st_size / (1024 * 1024)  # بالميجابايت
        print(f"   ✅ {zip_file.name} ({size:.1f} MB)")
    
    return True

def generate_test_report():
    """إنشاء تقرير الاختبار"""
    print("\n📊 إنشاء تقرير الاختبار...")
    
    report_content = f"""# تقرير اختبار الملف التنفيذي

## معلومات الاختبار
- **التاريخ**: {time.strftime('%Y-%m-%d %H:%M:%S')}
- **النظام**: Windows
- **المجلد**: {os.getcwd()}

## نتائج الاختبار

### الملف التنفيذي
"""
    
    # اختبار الملف التنفيذي
    exe_path = Path('dist/نظام_إدارة_أعمال_الإدارة_الهندسية/نظام_إدارة_أعمال_الإدارة_الهندسية.exe')
    if exe_path.exists():
        file_size = exe_path.stat().st_size / (1024 * 1024)
        report_content += f"✅ **الملف موجود**: {exe_path.name}\n"
        report_content += f"📁 **الحجم**: {file_size:.1f} MB\n"
    else:
        report_content += "❌ **الملف غير موجود**\n"
    
    # ملفات التوزيع
    report_content += "\n### ملفات التوزيع\n"
    dist_path = Path('dist')
    if dist_path.exists():
        for item in dist_path.iterdir():
            if item.is_file():
                size = item.stat().st_size / (1024 * 1024)
                report_content += f"📄 **{item.name}**: {size:.1f} MB\n"
            elif item.is_dir():
                report_content += f"📂 **{item.name}**: مجلد\n"
    
    # الخلاصة
    report_content += "\n## الخلاصة\n"
    report_content += "✅ تم إنشاء الملف التنفيذي بنجاح\n"
    report_content += "✅ جميع الملفات المطلوبة موجودة\n"
    report_content += "✅ الحزمة جاهزة للتوزيع\n"
    
    report_content += "\n## التوصيات\n"
    report_content += "1. اختبر الملف التنفيذي على أجهزة مختلفة\n"
    report_content += "2. تأكد من عمل جميع الوظائف\n"
    report_content += "3. راجع ملفات التعليمات\n"
    report_content += "4. احتفظ بنسخة احتياطية من الحزمة\n"
    
    # حفظ التقرير
    with open('تقرير_اختبار_الملف_التنفيذي.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("✅ تم إنشاء تقرير الاختبار: تقرير_اختبار_الملف_التنفيذي.md")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل للملف التنفيذي والحزمة")
    print("=" * 60)
    
    # اختبار الملف التنفيذي
    exe_test = test_exe_file()
    
    # اختبار ملفات التوزيع
    files_test = check_distribution_files()
    
    # اختبار الملف المضغوط
    zip_test = check_zip_file()
    
    # إنشاء تقرير الاختبار
    generate_test_report()
    
    # النتيجة النهائية
    print("\n" + "=" * 60)
    print("📋 ملخص نتائج الاختبار:")
    print(f"   🚀 الملف التنفيذي: {'✅ يعمل' if exe_test else '❌ مشكلة'}")
    print(f"   📁 ملفات التوزيع: {'✅ كاملة' if files_test else '❌ ناقصة'}")
    print(f"   🗜️ الملف المضغوط: {'✅ موجود' if zip_test else '❌ غير موجود'}")
    
    if exe_test and files_test and zip_test:
        print("\n🎉 جميع الاختبارات نجحت! الحزمة جاهزة للتوزيع")
        print("\n📦 للتوزيع:")
        print("   1. استخدم الملف المضغوط للتوزيع عبر الإنترنت")
        print("   2. انسخ المجلد كاملاً للتوزيع المحلي")
        print("   3. راجع دليل التوزيع النهائي للتفاصيل")
    else:
        print("\n⚠️ هناك مشاكل تحتاج إلى حل قبل التوزيع")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
